# Dependencies
/node_modules/
/.pnp
.pnp.js
.yarn/
.pnpm/
web_modules/
jspm_packages/
bower_components/

# Build and Production
/build/
/dist/
/.next/
/out/
/.swc/
/.vercel/
/.cache/
/.turbo/
/.turbo-cache/
/.webpack/
/.parcel-cache/
.fusebox/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Development Environments
/.clinerules/
.clinerules
/.trae/
/.augment.json
/.augmentignore
/.context-include
/.sentrycliignore
@.roo/
*.roo
.grid
.kilocode

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env*.local
.env.sentry-build-plugin

# Logs and Debugging
/logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
debug-*.js
capture-console-errors.js
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Testing
/coverage/
/.nyc_output/
/__tests__/
/__mocks__/
/__snapshots__/
*.lcov
jest.config.js
jest.setup.js
# diagnose-mongodb.cjs - REMOVED: Database diagnostic utility needed for development
# test-db-connection.cjs - REMOVED: Connection test utility needed for development
test-ssl-connection.cjs
test-mcp-api.js
run-all-api-tests.js
run-single-collection.js
run-tests.js
verify-mongodb-data.cjs
test-api-endpoint.js

# IDE and Editor Settings
/.idea/
/.vscode/
*.swp
*.swo
.cursor/
*.cursor
.cursorignore
*.sublime*
.project
.classpath
.c9/
*.launch
.settings/

# Database and MongoDB
*.sqlite
*.sqlite3
*.db
*.db3
mcp-postman/
mcp-postman-client.js
mcp-postman-config.json
mcp-postman-runner.js
postman-collections/
# init-mongodb.cjs - REMOVED: Database seeding script needed for development
# create-env.cjs - REMOVED: Environment setup utility needed for development
#check-env.cjs
mongodb.config.json
app/services/mongodb.ts.debug

# Instrumentation and Monitoring
sentry-mcp/
instrumentation.ts
instrumentation-client.ts
sentry.edge.config.ts
sentry.server.config.ts
mcp-taskmanager/
sentry-overrides.d.ts

# TypeScript and JavaScript
@types/
*.tsbuildinfo
next-env.d.ts
tsconfig.app.json
tsconfig.node.json
*.chunk.js
*.chunk.css
*.js.map
*.css.map
test-assembly-fix.js
test-complete-fix.js
debug-part-search.js

# System Files
.DS_Store
**/.DS_Store
Thumbs.db
*.stackdump
Desktop.ini
$RECYCLE.BIN/
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Backup and Temporary Files
*.tmp
*.temp
.git-backup/
/backup/
/js_backup/
/sql_backup/
/split_sql/
*_backup/
*.bak
*.backup
*.old
migration-audit-report.json
migration-audit-report.md
assemblies.html

# Development Artifacts
Tasks_*.md
*-analysis.md
*-fixes.md
*-summary.md
*-plan.md
*-guide.md
component-consolidation-*.md
import-dependencies-*.md
production-optimization-*.md
test-execution-*.md

# Unused Scripts and Development Utilities
scripts/assign-categories.js
scripts/check-assembly-duplicates.js
scripts/fix-inventory-frontend.js
scripts/migrate-category-ids.js
scripts/optimize-indexes.js
scripts/performance-monitoring.js
scripts/resolve-sentry-*.ps1
scripts/resolve-sentry-*.js
scripts/run-*.sh
scripts/test-enhanced-ui.ts
scripts/update-inventory-data.js
scripts/README_sentry.md

# Redundant Directories
/lib/
lib/services/
lib/utils.ts

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Documentation Files
*.md
!README.md
!REQUIREMENTS.md
# !docs/**/*.md
!API_DOCUMENTATION.md
!docs/ui-ux-audit-report.md
!docs/README.md
!docs/database_schema_updated.md
!docs/comprehensive-developer-guide.md

# Specific Project Directories
/tasks/
/task_list_testing/

# Generated Content
storybook-static/
build-storybook.log
*.generated.*
.dynamodb/

/test-results/
/tests/
/test-output/
/playwright-report/
/playwright.config.js

/postman-test/
# !sentry_issues_report.md
# !Tasks_2025-06-25T20-33-37.md
scripts/test-hierarchical-bom.js
scripts/test-integration-performance.js
.kiro/specs/inventory-production-planning/requirements.md
scripts/migrations/migrate-parts-to-nested-inventory.js
test-part-form-bug.js
commit_message.txt
test-inventory-pagination.js
inventory-pagination-test.png
test-pagination-final.js
test-pagination-simple.js
.gitignore
test-pagination.js
