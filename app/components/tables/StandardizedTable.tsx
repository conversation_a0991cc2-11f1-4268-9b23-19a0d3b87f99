"use client";

import React, { useState, useMemo, ReactNode, useEffect, useRef } from 'react';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { DataTableColumn } from '@/app/components/data-display/data-table/types';
import { cn } from '@/app/lib/utils';
import { Search, Table, LayoutGrid, List, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  SortingState,
  ColumnFiltersState,
  PaginationState,
} from '@tanstack/react-table';
import {
  Table as TableComponent,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/data-display/table';
import { DataTableToolbar } from '@/app/components/data-display/data-table/DataTableToolbar';
import { MobileCardView } from '@/app/components/data-display/data-table/MobileCardView';
import { DataTableSkeleton } from '@/app/components/data-display/data-table/DataTableSkeleton';
import { DataTableError } from '@/app/components/data-display/data-table/DataTableError';
import { DataTableEmpty } from '@/app/components/data-display/data-table/DataTableEmpty';

/**
 * View mode options for the standardized table
 */
export type ViewMode = 'table' | 'grid' | 'detailed';

/**
 * View option configuration
 */
export interface ViewOption {
  id: ViewMode;
  label: string;
  icon: ReactNode;
}

/**
 * Props for the StandardizedTable component
 */
export interface StandardizedTableProps<T> {
  /** Data to display */
  data: T[];
  /** Column definitions for table view */
  columns: DataTableColumn<T>[];
  /** Search placeholder text */
  searchPlaceholder?: string;
  /** Available view modes */
  viewOptions?: ViewOption[];
  /** Default view mode */
  defaultViewMode?: ViewMode;
  /** Whether to show search input */
  enableSearch?: boolean;
  /** Whether to show view mode toggle */
  enableViewToggle?: boolean;
  /** Custom filter controls to render */
  renderFilters?: () => ReactNode;
  /** Custom actions to render in toolbar */
  renderActions?: () => ReactNode;
  /** Custom grid component for grid view */
  GridComponent?: React.ComponentType<{ data: T[]; searchTerm: string }>;
  /** Custom detailed component for detailed view */
  DetailedComponent?: React.ComponentType<{ data: T[]; searchTerm: string }>;
  /** Container className */
  className?: string;
  /** Callback when view mode changes */
  onViewModeChange?: (mode: ViewMode) => void;
  /** Callback when search term changes */
  onSearchChange?: (term: string) => void;

  // Table-specific props (consolidated from DataTable)
  /** Whether to enable sorting */
  enableSorting?: boolean;
  /** Whether to enable filtering */
  enableFiltering?: boolean;
  /** Whether to enable global search */
  enableGlobalSearch?: boolean;
  /** Whether to enable pagination */
  enablePagination?: boolean;
  /** Whether to enable row selection */
  enableRowSelection?: boolean;
  /** Whether to enable column visibility toggle */
  enableColumnVisibility?: boolean;
  /** Mobile display mode */
  mobileDisplayMode?: 'cards' | 'horizontal-scroll' | 'stacked';
  /** Table density */
  density?: 'compact' | 'normal' | 'comfortable';
  /** Initial sorting state */
  initialSorting?: SortingState;
  /** Initial column filters */
  initialColumnFilters?: ColumnFiltersState;
  /** Initial global filter */
  initialGlobalFilter?: string;
  /**
   * Pagination state for the table.
   * - For client-side pagination (manualPagination=false): This sets the initial pagination state
   * - For server-side pagination (manualPagination=true): This should be the current pagination state (controlled)
   */
  initialPagination?: PaginationState;
  /** Initial column visibility state */
  initialColumnVisibility?: Record<string, boolean>;
  /** Page size options */
  pageSizeOptions?: number[];
  /**
   * Whether to use server-side pagination (controlled mode).
   * - true: Table pagination is controlled by parent component via initialPagination prop
   * - false: Table manages its own pagination state internally (uncontrolled mode)
   */
  manualPagination?: boolean;
  /** Total number of rows for server-side pagination (required when manualPagination=true) */
  totalRows?: number | undefined;
  /** Whether the data is loading */
  isLoading?: boolean;
  /** Loading skeleton rows count */
  loadingRows?: number;
  /** Error state */
  error?: Error | null;
  /** Whether to show table caption for accessibility */
  showCaption?: boolean;
  /** Table caption text */
  caption?: string;
  /** Whether to use sticky header */
  stickyHeader?: boolean;
  /** Maximum height for the table container */
  maxHeight?: string;
  /** Callback for when sorting changes */
  onSortingChange?: (sorting: SortingState) => void;
  /** Callback for when column filters change */
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void;
  /** Callback for when global filter changes */
  onGlobalFilterChange?: (filter: string) => void;
  /**
   * Callback for when pagination changes.
   * - For server-side pagination: Parent should update its state and pass new initialPagination
   * - For client-side pagination: Optional callback to be notified of internal state changes
   */
  onPaginationChange?: ((pagination: PaginationState) => void) | undefined;
  /** Callback for when row selection changes */
  onRowSelectionChange?: (selection: Record<string, boolean>) => void;
  /** Callback for when column visibility changes */
  onColumnVisibilityChange?: (visibility: Record<string, boolean>) => void;
  /** Callback for when a row is clicked */
  onRowClick?: (row: T) => void;
  /** Render function for row actions */
  renderRowActions?: (row: T) => ReactNode;
  /** Render function for table toolbar */
  renderToolbar?: () => ReactNode;
  /** Render function for empty state */
  renderEmptyState?: () => ReactNode;
  /** Render function for loading state */
  renderLoadingState?: () => ReactNode;
  /** Render function for error state */
  renderErrorState?: (error: Error) => ReactNode;
  /** Custom mobile card renderer */
  renderMobileCard?: (row: T, index: number) => ReactNode;

  // DEPRECATED: Legacy tableProps support for backward compatibility
  /** @deprecated Use individual props instead. Legacy table props object for backward compatibility */
  tableProps?: Partial<{
    enableSorting: boolean;
    enableFiltering: boolean;
    enableGlobalSearch: boolean;
    enablePagination: boolean;
    enableRowSelection: boolean;
    enableColumnVisibility: boolean;
    mobileDisplayMode: 'cards' | 'horizontal-scroll' | 'stacked';
    density: 'compact' | 'normal' | 'comfortable';
    initialSorting: SortingState;
    initialColumnFilters: ColumnFiltersState;
    initialGlobalFilter: string;
    initialPagination: PaginationState;
    initialColumnVisibility: Record<string, boolean>;
    pageSizeOptions: number[];
    manualPagination: boolean;
    totalRows: number;
    isLoading: boolean;
    loadingRows: number;
    error: Error | null;
    showCaption: boolean;
    caption: string;
    stickyHeader: boolean;
    maxHeight: string;
    onSortingChange: (sorting: SortingState) => void;
    onColumnFiltersChange: (filters: ColumnFiltersState) => void;
    onGlobalFilterChange: (filter: string) => void;
    onPaginationChange: (pagination: PaginationState) => void;
    onRowSelectionChange: (selection: Record<string, boolean>) => void;
    onColumnVisibilityChange: (visibility: Record<string, boolean>) => void;
    onRowClick: (row: T) => void;
    renderRowActions: (row: T) => ReactNode;
    renderToolbar: () => ReactNode;
    renderEmptyState: () => ReactNode;
    renderLoadingState: () => ReactNode;
    renderErrorState: (error: Error) => ReactNode;
    renderMobileCard: (row: T, index: number) => ReactNode;
  }>;
}

/**
 * Default view options
 */
const DEFAULT_VIEW_OPTIONS: ViewOption[] = [
  { id: 'table', label: 'Table', icon: <Table className="h-4 w-4" /> },
  { id: 'grid', label: 'Grid', icon: <LayoutGrid className="h-4 w-4" /> },
  { id: 'detailed', label: 'Detailed', icon: <List className="h-4 w-4" /> },
];

/**
 * StandardizedTable component - A unified table wrapper with consistent controls
 * 
 * Features:
 * - Unified search functionality
 * - View mode toggle (table/grid/detailed)
 * - Professional styling without bg-card wrappers
 * - Configurable toolbar with custom actions and filters
 * - Consistent UI/UX across all data tables
 */
export function StandardizedTable<T>({
  data,
  columns,
  searchPlaceholder = "Search...",
  viewOptions = DEFAULT_VIEW_OPTIONS,
  defaultViewMode = 'table',
  enableSearch = true,
  enableViewToggle = true,
  renderFilters,
  renderActions,
  GridComponent,
  DetailedComponent,
  className,
  onViewModeChange,
  onSearchChange,
  // Table-specific props
  enableSorting = true,
  enableFiltering = true,
  enableGlobalSearch = true,
  enablePagination = true,
  enableRowSelection = false,
  enableColumnVisibility = true,
  mobileDisplayMode = 'cards',
  density = 'normal',
  initialSorting = [],
  initialColumnFilters = [],
  initialGlobalFilter = '',
  initialPagination = { pageIndex: 0, pageSize: 50 },
  initialColumnVisibility = {},
  pageSizeOptions = [10, 20, 50, 100, 150],
  manualPagination = false,
  totalRows,
  isLoading = false,
  loadingRows = 5,
  error = null,
  showCaption = true,
  caption,
  stickyHeader = false,
  maxHeight,
  onSortingChange,
  onColumnFiltersChange,
  onGlobalFilterChange,
  onPaginationChange,
  onRowSelectionChange,
  onColumnVisibilityChange,
  onRowClick,
  renderRowActions,
  renderToolbar,
  renderEmptyState,
  renderLoadingState,
  renderErrorState,
  renderMobileCard,
  // Legacy tableProps support
  tableProps,
}: StandardizedTableProps<T>) {
  // BACKWARD COMPATIBILITY: Merge tableProps with direct props
  // Direct props take precedence over tableProps
  const resolvedProps = {
    enableSorting: enableSorting ?? tableProps?.enableSorting ?? true,
    enableFiltering: enableFiltering ?? tableProps?.enableFiltering ?? true,
    enableGlobalSearch: enableGlobalSearch ?? tableProps?.enableGlobalSearch ?? true,
    enablePagination: enablePagination ?? tableProps?.enablePagination ?? true,
    enableRowSelection: enableRowSelection ?? tableProps?.enableRowSelection ?? false,
    enableColumnVisibility: enableColumnVisibility ?? tableProps?.enableColumnVisibility ?? true,
    mobileDisplayMode: mobileDisplayMode ?? tableProps?.mobileDisplayMode ?? 'cards',
    density: density ?? tableProps?.density ?? 'normal',
    initialSorting: initialSorting ?? tableProps?.initialSorting ?? [],
    initialColumnFilters: initialColumnFilters ?? tableProps?.initialColumnFilters ?? [],
    initialGlobalFilter: initialGlobalFilter ?? tableProps?.initialGlobalFilter ?? '',
    initialPagination: initialPagination ?? tableProps?.initialPagination ?? { pageIndex: 0, pageSize: 50 },
    initialColumnVisibility: initialColumnVisibility ?? tableProps?.initialColumnVisibility ?? {},
    pageSizeOptions: pageSizeOptions ?? tableProps?.pageSizeOptions ?? [10, 20, 50, 100, 150],
    manualPagination: manualPagination ?? tableProps?.manualPagination ?? false,
    totalRows: totalRows ?? tableProps?.totalRows,
    isLoading: isLoading ?? tableProps?.isLoading ?? false,
    loadingRows: loadingRows ?? tableProps?.loadingRows ?? 5,
    error: error ?? tableProps?.error ?? null,
    showCaption: showCaption ?? tableProps?.showCaption ?? true,
    caption: caption ?? tableProps?.caption,
    stickyHeader: stickyHeader ?? tableProps?.stickyHeader ?? false,
    maxHeight: maxHeight ?? tableProps?.maxHeight,
    onSortingChange: onSortingChange ?? tableProps?.onSortingChange,
    onColumnFiltersChange: onColumnFiltersChange ?? tableProps?.onColumnFiltersChange,
    onGlobalFilterChange: onGlobalFilterChange ?? tableProps?.onGlobalFilterChange,
    onPaginationChange: onPaginationChange ?? tableProps?.onPaginationChange,
    onRowSelectionChange: onRowSelectionChange ?? tableProps?.onRowSelectionChange,
    onColumnVisibilityChange: onColumnVisibilityChange ?? tableProps?.onColumnVisibilityChange,
    onRowClick: onRowClick ?? tableProps?.onRowClick,
    renderRowActions: renderRowActions ?? tableProps?.renderRowActions,
    renderToolbar: renderToolbar ?? tableProps?.renderToolbar,
    renderEmptyState: renderEmptyState ?? tableProps?.renderEmptyState,
    renderLoadingState: renderLoadingState ?? tableProps?.renderLoadingState,
    renderErrorState: renderErrorState ?? tableProps?.renderErrorState,
    renderMobileCard: renderMobileCard ?? tableProps?.renderMobileCard,
  };

  // Log backward compatibility usage
  if (tableProps && Object.keys(tableProps).length > 0) {
    console.warn('[StandardizedTable] DEPRECATED: tableProps is deprecated. Please use individual props instead.');
    console.log('[StandardizedTable] tableProps received:', tableProps);
    console.log('[StandardizedTable] Resolved props:', {
      manualPagination: resolvedProps.manualPagination,
      totalRows: resolvedProps.totalRows,
    });
  }
  // Internal state for table functionality
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>(defaultViewMode);
  const [sorting, setSorting] = useState<SortingState>(initialSorting);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(initialColumnFilters);
  const [columnVisibility, setColumnVisibility] = useState<Record<string, boolean>>(initialColumnVisibility);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  // Pagination state management: controlled vs uncontrolled
  // For server-side pagination (manualPagination=true): use controlled pattern (derive from props)
  // For client-side pagination (manualPagination=false): use internal state
  const [internalPagination, setInternalPagination] = useState<PaginationState>(resolvedProps.initialPagination);

  // FIXED: Proper controlled/uncontrolled pagination pattern
  // For server-side (controlled): always use the prop value directly
  // For client-side (uncontrolled): use internal state that can be updated
  const pagination = useMemo(() => {
    if (resolvedProps.manualPagination) {
      // Controlled mode: use props directly, no internal state management
      console.log('[STANDARDIZED TABLE DEBUG] Using controlled pagination from props:', resolvedProps.initialPagination);
      return resolvedProps.initialPagination;
    } else {
      // Uncontrolled mode: use internal state
      console.log('[STANDARDIZED TABLE DEBUG] Using uncontrolled internal pagination:', internalPagination);
      return internalPagination;
    }
  }, [resolvedProps.manualPagination, resolvedProps.initialPagination, internalPagination]);



  // Create the table instance using useReactTable
  const table = useReactTable({
    data: manualPagination ? data : data, // Use all data for manual pagination, filtered for client-side
    columns: columns as any,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
      globalFilter: searchTerm,
    },
    enableRowSelection: resolvedProps.enableRowSelection,
    enableSorting: resolvedProps.enableSorting,
    enableColumnFilters: resolvedProps.enableFiltering,
    enableGlobalFilter: resolvedProps.enableGlobalSearch,
    manualPagination: resolvedProps.manualPagination,
    pageCount: resolvedProps.manualPagination ? (() => {
      const effectiveTotalRows = resolvedProps.totalRows || 0;
      // Fix zero-row edge case: ensure at least 1 page when totalRows is 0
      const calculatedPageCount = effectiveTotalRows > 0
        ? Math.ceil(effectiveTotalRows / pagination.pageSize)
        : 1;

      console.log('[STANDARDIZED TABLE DEBUG] Page count calculation:', {
        totalRows: resolvedProps.totalRows,
        effectiveTotalRows,
        pageSize: pagination.pageSize,
        calculatedPageCount,
        manualPagination: resolvedProps.manualPagination
      });
      return calculatedPageCount;
    })() : -1,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: (updater) => {
      const newSorting = typeof updater === 'function' ? updater(sorting) : updater;
      setSorting(newSorting);
      resolvedProps.onSortingChange?.(newSorting);
    },
    onColumnFiltersChange: (updater) => {
      const newFilters = typeof updater === 'function' ? updater(columnFilters) : updater;
      setColumnFilters(newFilters);
      resolvedProps.onColumnFiltersChange?.(newFilters);
    },
    onPaginationChange: (updater) => {
      const newPagination = typeof updater === 'function' ? updater(pagination) : updater;
      console.log('[STANDARDIZED TABLE DEBUG] Pagination change triggered:', {
        newPagination,
        currentPagination: pagination,
        manualPagination: resolvedProps.manualPagination,
        updaterType: typeof updater
      });

      if (resolvedProps.manualPagination) {
        // FIXED: For server-side pagination (controlled mode)
        // Only notify parent component, do NOT update any internal state
        // The parent will update its state and pass new props, causing a re-render
        console.log('[STANDARDIZED TABLE DEBUG] Controlled mode: notifying parent of pagination change');
        resolvedProps.onPaginationChange?.(newPagination);
      } else {
        // For client-side pagination (uncontrolled mode): update internal state
        console.log('[STANDARDIZED TABLE DEBUG] Uncontrolled mode: updating internal pagination state');
        setInternalPagination(newPagination);
        resolvedProps.onPaginationChange?.(newPagination);
      }
    },
    onRowSelectionChange: (updater) => {
      const newSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
      setRowSelection(newSelection);
      resolvedProps.onRowSelectionChange?.(newSelection);
    },
    onColumnVisibilityChange: (updater) => {
      const newVisibility = typeof updater === 'function' ? updater(columnVisibility) : updater;
      setColumnVisibility(newVisibility);
      resolvedProps.onColumnVisibilityChange?.(newVisibility);
    },
    onGlobalFilterChange: (filter) => {
      setSearchTerm(filter);
      resolvedProps.onGlobalFilterChange?.(filter);
    },
  });

  // Filter data based on search term - only for client-side search
  // For server-side pagination, search should be handled by the parent component
  const filteredData = useMemo(() => {
    // If using manual pagination, don't filter here - let the server handle it
    if (manualPagination) {
      return data;
    }

    if (!searchTerm.trim()) return data;

    const searchLower = searchTerm.toLowerCase();
    return data.filter((item: any) => {
      // Search through all string properties of the item
      return Object.values(item).some((value) => {
        if (typeof value === 'string') {
          return value.toLowerCase().includes(searchLower);
        }
        if (typeof value === 'number') {
          return value.toString().includes(searchLower);
        }
        return false;
      });
    });
  }, [data, searchTerm, manualPagination]);

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    onSearchChange?.(value);
  };

  // Handle view mode change
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    onViewModeChange?.(mode);
  };

  // Get available view options (filter out unavailable ones)
  const availableViewOptions = useMemo(() => {
    return viewOptions.filter(option => {
      return !((option.id === 'grid' && !GridComponent) || (option.id === 'detailed' && !DetailedComponent));
    });
  }, [viewOptions, GridComponent, DetailedComponent]);

  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* Standardized Toolbar - Matching batch-tracking design */}
      {(enableSearch || enableViewToggle || renderFilters || renderActions) && (
        <div className="flex items-center justify-between space-x-4">
          {/* Left side - Search and Filters */}
          <div className="flex flex-1 items-center space-x-2">
            {/* Search Input */}
            {enableSearch && (
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder={searchPlaceholder}
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  aria-label="Search table data"
                />
              </div>
            )}

            {/* Custom Filters */}
            {renderFilters?.()}
          </div>

          {/* Right side - View Toggle and Actions */}
          <div className="flex items-center gap-2">
            {/* Custom Actions */}
            {renderActions?.()}

            {/* View Mode Toggle */}
            {enableViewToggle && availableViewOptions.length > 1 && (
              <div className="flex items-center border border-border rounded-md">
                {availableViewOptions.map((option, index) => (
                  <Button
                    key={option.id}
                    variant={viewMode === option.id ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleViewModeChange(option.id)}
                    className={cn(
                      "h-8 px-3",
                      index === 0 && availableViewOptions.length > 1 && "rounded-r-none border-r border-border",
                      index > 0 && index < availableViewOptions.length - 1 && "rounded-none border-r border-border",
                      index === availableViewOptions.length - 1 && index > 0 && "rounded-l-none"
                    )}
                    title={option.label}
                  >
                    {option.icon}
                  </Button>
                ))}
              </div>
            )}


          </div>
        </div>
      )}

      {/* Content Container with Professional Styling - Matching batch-tracking design */}
      <div className="rounded-lg shadow-md dark:shadow-gray-900/30 overflow-hidden">
        {/* Render appropriate view based on mode */}
        {viewMode === 'table' && (
          <div className="w-full space-y-4">
            {/* Error state */}
            {error && (
              renderErrorState ? renderErrorState(error) : <DataTableError error={error} />
            )}

            {/* Loading state */}
            {isLoading && !error && (
              renderLoadingState ? renderLoadingState() : <DataTableSkeleton rows={loadingRows} />
            )}

            {/* Empty state */}
            {!isLoading && !error && !data.length && (
              renderEmptyState ? renderEmptyState() : <DataTableEmpty />
            )}

            {/* Table content */}
            {!isLoading && !error && data.length > 0 && (
              <>
                {/* Toolbar - Only render if it has content to show */}
                {/* When enableSearch is true, disable DataTableToolbar's global search to prevent duplication */}
                {((!enableSearch && enableGlobalSearch) || enableColumnVisibility || renderToolbar) && (
                  <DataTableToolbar
                    table={table}
                    enableGlobalSearch={!enableSearch && enableGlobalSearch}
                    enableColumnVisibility={enableColumnVisibility}
                  >
                    {renderToolbar?.()}
                  </DataTableToolbar>
                )}

                {/* Mobile view */}
                {mobileDisplayMode === 'cards' && (
                  <div className="block md:hidden">
                    <MobileCardView
                      data={table.getRowModel().rows.map(row => row.original)}
                      columns={columns}
                      renderRowActions={renderRowActions}
                      renderMobileCard={renderMobileCard || undefined}
                    />
                  </div>
                )}

                {/* Desktop table view */}
                <div className={cn(
                  mobileDisplayMode === 'cards' ? 'hidden md:block' : 'block',
                  'relative'
                )}>
                  <div className={cn(
                    'overflow-auto',
                    stickyHeader && 'max-h-[600px]',
                    maxHeight && `max-h-[${maxHeight}]`
                  )}>
                    <TableComponent className={cn(
                      'w-full caption-bottom text-sm',
                      density === 'compact' && 'text-xs',
                      density === 'comfortable' && 'text-base'
                    )}>
                      {showCaption && caption && (
                        <caption className="mt-4 text-sm text-muted-foreground">
                          {caption}
                        </caption>
                      )}
                      <TableHeader className={cn(stickyHeader && 'sticky top-0 z-10 bg-background')}>
                        {table.getHeaderGroups().map((headerGroup) => (
                          <TableRow key={headerGroup.id}>
                            {headerGroup.headers.map((header) => (
                              <TableHead key={header.id}>
                                {header.isPlaceholder
                                  ? null
                                  : flexRender(
                                      header.column.columnDef.header,
                                      header.getContext()
                                    )}
                              </TableHead>
                            ))}
                          </TableRow>
                        ))}
                      </TableHeader>
                      <TableBody>
                        {table.getRowModel().rows.map((row) => (
                          <TableRow
                            key={row.id}
                            data-state={row.getIsSelected() && 'selected'}
                            className={cn(
                              onRowClick && 'cursor-pointer hover:bg-muted/50',
                              'transition-colors'
                            )}
                            onClick={() => onRowClick?.(row.original)}
                          >
                            {row.getVisibleCells().map((cell) => (
                              <TableCell key={cell.id}>
                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </TableComponent>
                  </div>
                </div>

                {/* Pagination */}
                {enablePagination && (
                  <div className="p-4 border-t border-border/30">
                    {/* Inline pagination component */}
                    <div className="flex items-center justify-between px-2">
                      <div className="flex-1 text-sm text-muted-foreground">
                        {table.getFilteredSelectedRowModel().rows.length} of{" "}
                        {table.getFilteredRowModel().rows.length} row(s) selected.
                      </div>
                      <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium">Rows per page</p>
                          <Select
                            value={`${table.getState().pagination.pageSize}`}
                            onValueChange={(value) => {
                              table.setPageSize(Number(value));
                            }}
                          >
                            <SelectTrigger className="h-8 w-[70px]">
                              <SelectValue placeholder={table.getState().pagination.pageSize} />
                            </SelectTrigger>
                            <SelectContent side="top">
                              {pageSizeOptions.map((pageSize) => (
                                <SelectItem key={pageSize} value={`${pageSize}`}>
                                  {pageSize}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                          Page {Math.min(table.getState().pagination.pageIndex + 1, table.getPageCount())} of{" "}
                          {(() => {
                            const pageCount = table.getPageCount();
                            console.log('[PAGINATION DEBUG] Page count display:', {
                              pageCount,
                              currentPageIndex: table.getState().pagination.pageIndex,
                              manualPagination: resolvedProps.manualPagination,
                              dataLength: data.length,
                              filteredRowsLength: table.getFilteredRowModel().rows.length,
                              totalRows: resolvedProps.totalRows
                            });

                            // Always use the table's calculated page count for consistency
                            return pageCount > 0 ? pageCount : 1;
                          })()}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            className="hidden h-8 w-8 p-0 lg:flex"
                            onClick={(e) => {
                              e.preventDefault();
                              console.log('[PAGINATION DEBUG] First page button clicked');
                              table.setPageIndex(0);
                            }}
                            disabled={!table.getCanPreviousPage()}
                            aria-label="Go to first page"
                          >
                            <ChevronsLeft className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            className="h-8 w-8 p-0"
                            onClick={(e) => {
                              e.preventDefault();
                              console.log('[PAGINATION DEBUG] Previous page button clicked');
                              table.previousPage();
                            }}
                            disabled={!table.getCanPreviousPage()}
                            aria-label="Go to previous page"
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            className="h-8 w-8 p-0"
                            onClick={(e) => {
                              e.preventDefault();
                              console.log('[PAGINATION DEBUG] Next page button clicked');
                              table.nextPage();
                            }}
                            disabled={!table.getCanNextPage()}
                            aria-label="Go to next page"
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            className="hidden h-8 w-8 p-0 lg:flex"
                            onClick={(e) => {
                              e.preventDefault();
                              console.log('[PAGINATION DEBUG] Last page button clicked');
                              const pageCount = table.getPageCount() > 0 ? table.getPageCount() : 1;
                              table.setPageIndex(pageCount - 1);
                            }}
                            disabled={!table.getCanNextPage()}
                            aria-label="Go to last page"
                          >
                            <ChevronsRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {viewMode === 'grid' && GridComponent && (
          <div className="p-4">
            <GridComponent data={filteredData} searchTerm={searchTerm} />
          </div>
        )}

        {viewMode === 'detailed' && DetailedComponent && (
          <div className="p-4">
            <DetailedComponent data={filteredData} searchTerm={searchTerm} />
          </div>
        )}
      </div>
    </div>
  );
}
