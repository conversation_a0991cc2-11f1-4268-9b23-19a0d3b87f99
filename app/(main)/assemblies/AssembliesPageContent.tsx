'use client';

import { motion } from 'framer-motion';
import { Layers, LayoutGrid, List, SlidersHorizontal, Table } from 'lucide-react';
import Link from 'next/link';
import React, { useCallback, useEffect, useState, useMemo } from 'react';

import { RecentAssemblies } from '@/app/components/assemblies/RecentAssemblies';
import { AutoRefreshControl } from '@/app/components/controls/AutoRefreshControl';
import { Button } from '@/app/components/forms/Button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { CardContent } from '@/app/components/layout/cards/card';
import Header from '@/app/components/layout/Header';
import { AssemblyFormModal } from '@/app/components/modals/AssemblyFormModal';
import { AssembliesGrid } from '@/app/components/tables/AssembliesGrid';
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { createAssembliesColumns, AssemblyColumnData, AssembliesTableActions } from '@/app/components/data-display/data-table/column-definitions';
import { useTheme } from '@/app/context/ThemeContext';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import { useAssemblyUIPreferences } from '@/app/hooks/useAssemblyUIPreferences';
import { toast } from 'sonner';

/**
 * Assemblies page content component - displays list of assemblies
 */
const AssembliesPageContent: React.FC = () => {
  const { theme: _ } = useTheme(); // Unused but kept for context
  const {
    assemblies,
    isLoading,
    error,
    refreshAssemblies
  } = useAssemblies();

  // Use UI preferences hook to persist user preferences
  const {
    preferences,
    updatePreference,
    updatePreferences,
    isLoaded
  } = useAssemblyUIPreferences();

  // Initialize state from preferences (defaulting until preferences are loaded)
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState(preferences.searchQuery || '');
  const [statusFilter, setStatusFilter] = useState(preferences.filterStatus || '');
  const [sortBy, setSortBy] = useState(preferences.sortBy || 'name');
  const [viewMode, setViewMode] = useState<'grid' | 'table'>(preferences.viewMode || 'grid');

  // Sync state with preferences once loaded
  useEffect(() => {
    if (isLoaded) {
      setSearchQuery(preferences.searchQuery);
      setStatusFilter(preferences.filterStatus);
      setSortBy(preferences.sortBy);
      setViewMode(preferences.viewMode);
    }
  }, [isLoaded, preferences]);

  // Updates preference when value changes
  useEffect(() => {
    if (!isLoaded) return;
    updatePreference('searchQuery', searchQuery);
  }, [searchQuery, updatePreference, isLoaded]);

  useEffect(() => {
    if (!isLoaded) return;
    updatePreference('filterStatus', statusFilter);
  }, [statusFilter, updatePreference, isLoaded]);

  useEffect(() => {
    if (!isLoaded) return;
    updatePreference('sortBy', sortBy);
  }, [sortBy, updatePreference, isLoaded]);

  // Fetch assemblies with parts data for both grid and table views
  useEffect(() => {
    // Always fetch with parts data for both grid and table views
    if (isLoaded) { // Only refresh if preferences are loaded
      refreshAssemblies({ includeParts: true });
    }
  }, [viewMode, isLoaded]); // Remove refreshAssemblies dependency to prevent infinite loop

  // Helper function to determine assembly status based on partsRequired
  const getAssemblyStatus = (assembly: any): 'complete' | 'incomplete' | 'review' => {
    // Safely handle potentially missing partsRequired field
    if (!assembly) return 'incomplete';

    const hasValidParts = assembly.partsRequired &&
      Array.isArray(assembly.partsRequired) &&
      assembly.partsRequired.length > 0;

    if (!hasValidParts) {
      return 'incomplete';
    }

    // Check for missing partId references within partsRequired
    const hasMissingRefs = assembly.partsRequired.some((p: any) =>
      !p || p.partId === undefined || p.partId === null);

    if (hasMissingRefs) {
      return 'review';
    }

    return 'complete';
  };

  // Filter assemblies based on search query and status filter
  const filteredAssemblies = assemblies
    .filter(assembly => {
      // Skip invalid assemblies - use assemblyCode from new schema
      if (!assembly || !assembly.name) {
        console.warn('[AssembliesPage] Filtering out invalid assembly:', assembly);
        return false;
      }

      // Search by name, assemblyCode (new schema), or _id
      const matchesSearch =
        !searchQuery ||
        assembly.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (assembly.assemblyCode && assembly.assemblyCode.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (assembly._id && assembly._id.toLowerCase().includes(searchQuery.toLowerCase()));

      // Filter by status (using the status field from schema)
      const matchesStatus = !statusFilter || statusFilter === 'all' || assembly.status === statusFilter;

      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      // Sort based on selected sort option
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'name_desc':
          return b.name.localeCompare(a.name);
        case 'parts_count':
          // Safely handle potentially missing partsRequired
          const aCount = a.partsRequired && Array.isArray(a.partsRequired) ? a.partsRequired.length : 0;
          const bCount = b.partsRequired && Array.isArray(b.partsRequired) ? b.partsRequired.length : 0;
          return bCount - aCount; // Descending order (most parts first)
        case 'updated':
          // Check for standard updated fields with fallback to _id
          const aDate = a.updatedAt ? new Date(a.updatedAt).getTime() :
                     a.createdAt ? new Date(a.createdAt).getTime() :
                     a._id ? parseInt(a._id.substring(0, 8), 16) * 1000 : 0;
          const bDate = b.updatedAt ? new Date(b.updatedAt).getTime() :
                     b.createdAt ? new Date(b.createdAt).getTime() :
                     b._id ? parseInt(b._id.substring(0, 8), 16) * 1000 : 0;
          return bDate - aDate; // Descending order (newest first)
        default:
          return a.name.localeCompare(b.name);
      }
    });

  // Transform assemblies data for table display
  const tableData: AssemblyColumnData[] = useMemo(() => {
    return filteredAssemblies.map(assembly => ({
      _id: assembly._id,
      assemblyCode: assembly.assemblyCode,
      name: assembly.name,
      description: assembly.description || '',
      status: assembly.status || 'active',
      partsRequired: assembly.partsRequired || [],
      productId: assembly.productId || null,
      parentId: assembly.parentId || null,
      isTopLevel: assembly.isTopLevel,
      version: assembly.version,
      manufacturingInstructions: assembly.manufacturingInstructions,
      estimatedBuildTime: assembly.estimatedBuildTime,
      createdAt: assembly.createdAt,
      updatedAt: assembly.updatedAt,
    }));
  }, [filteredAssemblies]);

  // Create table actions
  const tableActions: AssembliesTableActions = useMemo(() => ({
    onRefresh: () => refreshAssemblies(),
  }), [refreshAssemblies]);

  // Create columns for table view
  const columns = useMemo(() => {
    return createAssembliesColumns(tableActions, false);
  }, [tableActions]);

  // Handle view mode change
  const handleViewModeChange = useCallback((mode: 'grid' | 'table') => {
    setViewMode(mode);
    if (isLoaded) {
      updatePreference('viewMode', mode);
    }

    if (mode === 'grid') {
      // When switching to grid view, fetch with parts data
      refreshAssemblies({ includeParts: true });
      toast.success('Switched to Grid view', {
        description: 'Showing assemblies in a card-based layout',
        duration: 2000
      });
    } else {
      toast.success('Switched to Table view', {
        description: 'Showing assemblies in a detailed table',
        duration: 2000
      });
    }
  }, [isLoaded, updatePreference, refreshAssemblies]);

  return (
    <div className="flex-1 overflow-y-auto bg-background text-foreground">
      <Header title="Assemblies" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            {/* Create Assembly Section - Using Modal */}
            <AssemblyFormModal onSuccess={refreshAssemblies} />
          </div>

          <div className="flex items-center space-x-2">
            {/* Auto-refresh Control */}
            {isLoaded && <AutoRefreshControl />}
          </div>
        </div>

        {/* Recent Assemblies */}
        {isLoaded && <RecentAssemblies />}



        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-500 dark:text-gray-400">Loading assemblies...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">Error Loading Assemblies</h3>
            <p className="text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && !error && filteredAssemblies.length === 0 && (
          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
            <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Assemblies Found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              {searchQuery || statusFilter
                ? "No assemblies match your current filters. Try adjusting your search or filter criteria."
                : "There are no assemblies in the system yet."}
            </p>
            {!(searchQuery || statusFilter) && (
              <Button asChild>
                <Link href="/assemblies/create">Create Assembly</Link>
              </Button>
            )}
          </div>
        )}

        {/* Results */}
        {!isLoading && !error && filteredAssemblies.length > 0 && (
          <StandardizedTable
            data={tableData}
            columns={columns}
            searchPlaceholder="Search assemblies by name, code, or ID..."
            viewOptions={[
              { id: 'grid', label: 'Grid', icon: <LayoutGrid className="h-4 w-4" /> },
              { id: 'table', label: 'Table', icon: <Table className="h-4 w-4" /> },
            ]}
            defaultViewMode="grid"
            enableSearch={true}
            enableViewToggle={true}
            GridComponent={({ data }) => <AssembliesGrid assemblies={data as any} />}
            renderFilters={() => (
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  className="rounded-full"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <SlidersHorizontal size={16} className="mr-2" />
                  <span>Filters</span>
                </Button>

                {showFilters && (
                  <>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-40 h-9">
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="pending_review">Pending Review</SelectItem>
                        <SelectItem value="in_production">In Production</SelectItem>
                        <SelectItem value="obsolete">Obsolete</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-40 h-9">
                        <SelectValue placeholder="Sort By" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="name">Name (A-Z)</SelectItem>
                        <SelectItem value="name_desc">Name (Z-A)</SelectItem>
                        <SelectItem value="parts_count">Parts Count</SelectItem>
                        <SelectItem value="updated">Last Updated</SelectItem>
                      </SelectContent>
                    </Select>
                  </>
                )}
              </div>
            )}
            renderActions={() => (
              <div className="flex items-center gap-2">
                <AssemblyFormModal onSuccess={refreshAssemblies} />
                {isLoaded && <AutoRefreshControl />}
              </div>
            )}
            enablePagination={true}
            enableSorting={true}
            enableFiltering={true}
            enableGlobalSearch={false}
            enableColumnVisibility={false}
            mobileDisplayMode="cards"
            density="normal"
            initialPagination={{ pageIndex: 0, pageSize: 20 }}
            pageSizeOptions={[10, 20, 50, 100]}
            isLoading={isLoading}
            error={error ? new Error(error) : null}
            onViewModeChange={(mode) => handleViewModeChange(mode as 'grid' | 'table')}
            onSearchChange={(term) => {
              setSearchQuery(term);
              if (isLoaded) {
                updatePreference('searchQuery', term);
              }
            }}
          />
        )}
      </div>
    </div>
  );
};

export default AssembliesPageContent;
