"use client";

import { Badge } from '@/app/components/data-display/badge';
import { Button } from '@/app/components/forms/Button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/app/components/navigation/DropdownMenu';
import { BatchStats, BatchStatsData } from '@/app/components/features/BatchStats';
import { StandardizedTable, type ViewMode } from '@/app/components/tables/StandardizedTable';
import { createBatchesColumns, BatchColumnData, type BatchesTableActions } from '@/app/components/data-display/data-table/column-definitions';
import { asApiResponse, asAssembliesResponse, asPartsResponse, extractApiError, hasApiError } from '@/app/types/api-responses';
import { zodResolver } from '@hookform/resolvers/zod';
import { addDays, format, isBefore } from 'date-fns';
import { motion } from 'framer-motion';
import {
  ArrowUpDown,
  CheckCircle,
  Edit,
  Eye,
  Layers,
  Loader2,
  MoreHorizontal, // Added Play icon
  Pause, // Added Pause icon
  Play,
  Plus,
  RefreshCw,
  Trash2
} from 'lucide-react';
import React, { useEffect, useState, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { showNetworkErrorToast, showErrorToast, showSuccessToast } from '@/app/components/feedback';
import * as z from 'zod';
import Header from '../../../app/components/layout/Header';
import { useTheme } from '../../context/ThemeContext';

// Define interfaces for batch data
interface Part {
  _id: string;
  name: string;
  description?: string;
}

interface Assembly {
  _id: string;
  assemblyCode: string;
  name: string;
}

interface WorkOrder {
  _id: string;
  woNumber: string;
  status: string;
}

interface Batch {
  _id: string;
  batchCode: string;
  partId?: string | Part;
  assemblyId?: string | Assembly;
  quantityPlanned: number;
  quantityProduced?: number;
  startDate: string;
  endDate?: string;
  status: string;
  notes?: string;
  workOrderId: string | WorkOrder;
  createdAt: string;
  updatedAt: string;
}

interface BatchLog {
  _id: string;
  batchId: string;
  timestamp: string;
  event: string;
  userId: string;
  details?: string;
  createdAt: string;
  updatedAt: string;
}

// Define form schema
const batchFormSchema = z.object({
  batchCode: z.string().optional(),
  partId: z.string().optional(),
  assemblyId: z.string().optional(),
  quantityPlanned: z.number().min(1, "Quantity must be at least 1"),
  quantityProduced: z.number().optional(),
  startDate: z.string(),
  endDate: z.string().optional(),
  status: z.string(),
  notes: z.string().optional(),
  workOrderId: z.string()
});

const BatchTracking: React.FC = () => {
  const { theme } = useTheme();
  const [batches, setBatches] = useState<Batch[]>([]);
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [parts, setParts] = useState<Part[]>([]);
  const [assemblies, setAssemblies] = useState<Assembly[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentBatch, setCurrentBatch] = useState<Batch | null>(null);
  const [selectedBatch, setSelectedBatch] = useState<Batch | null>(null);
  const [batchLogs, setBatchLogs] = useState<BatchLog[]>([]);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentBatchAction, setCurrentBatchAction] = useState<'add' | 'edit'>('add');
  const [isLoadingAction, setIsLoadingAction] = useState(false);
  const [stats, setStats] = useState<BatchStatsData>({
    totalBatches: 0,
    activeBatches: 0,
    completedBatches: 0,
    pendingBatches: 0,
    onHoldBatches: 0,
    totalQuantityPlanned: 0,
    totalQuantityProduced: 0,
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [viewMode, setViewMode] = useState<ViewMode>('table');

  // Form setup with zod validation
  const { register, handleSubmit, formState: { errors }, reset } = useForm<z.infer<typeof batchFormSchema>>({
    resolver: zodResolver(batchFormSchema),
    defaultValues: {
      batchCode: '',
      partId: '',
      assemblyId: '',
      quantityPlanned: 1,
      quantityProduced: 0,
      startDate: format(new Date(), 'yyyy-MM-dd'),
      status: 'pending',
      notes: '',
      workOrderId: ''
    }
  });

  // Fetch data on component mount
  useEffect(() => {
    fetchBatches();
    fetchWorkOrders();
    fetchParts();
    fetchAssemblies();
  }, []);

  // Fetch batches from API
  const fetchBatches = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/batches');

      if (!response.ok) {
        throw new Error(`Error fetching batches: ${response.status}`);
      }

      const data = asApiResponse<Batch[]>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      // Transform batch data to ensure compatibility with DataTable
      const transformedBatches = (data.data || []).map((batch: any) => ({
        ...batch,
        id: batch._id, // Add id field for DataTable compatibility
        // Ensure workOrderId is properly handled
        workOrderNumber: batch.workOrderId?.woNumber || 'N/A',
        // Ensure dates are properly formatted
        startDate: batch.startDate ? new Date(batch.startDate) : null,
        endDate: batch.endDate ? new Date(batch.endDate) : null,
        createdAt: batch.createdAt ? new Date(batch.createdAt) : new Date(),
        updatedAt: batch.updatedAt ? new Date(batch.updatedAt) : new Date(),
      }));

      setBatches(transformedBatches);
    } catch (err) {
      console.error('Error fetching batches:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      showNetworkErrorToast(fetchBatches, { customMessage: 'Failed to load batch data' });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch work orders from API
  const fetchWorkOrders = async () => {
    try {
      const response = await fetch('/api/work-orders');

      if (!response.ok) {
        throw new Error(`Error fetching work orders: ${response.status}`);
      }

      const data = asApiResponse<WorkOrder[]>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      setWorkOrders(data.data || []);
    } catch (err) {
      console.error('Error fetching work orders:', err);
      showNetworkErrorToast(fetchWorkOrders, { customMessage: 'Failed to load work orders' });
    }
  };

  // Fetch parts from API
  const fetchParts = async () => {
    try {
      const response = await fetch('/api/parts');

      if (!response.ok) {
        throw new Error(`Error fetching parts: ${response.status}`);
      }

      const data = asPartsResponse(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      setParts(data.data || []);
    } catch (err) {
      console.error('Error fetching parts:', err);
      showNetworkErrorToast(fetchParts, { customMessage: 'Failed to load parts' });
    }
  };

  // Fetch assemblies from API
  const fetchAssemblies = async () => {
    try {
      const response = await fetch('/api/assemblies');

      if (!response.ok) {
        throw new Error(`Error fetching assemblies: ${response.status}`);
      }

      const data = asAssembliesResponse(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      setAssemblies(data.data || []);
    } catch (err) {
      console.error('Error fetching assemblies:', err);
      showNetworkErrorToast(fetchAssemblies, { customMessage: 'Failed to load assemblies' });
    }
  };

  // Fetch batch logs from API
  const fetchBatchLogs = async (batchId: string) => {
    if (!batchId) return;
    try {
      const response = await fetch(`/api/batches/${batchId}/logs`);

      if (!response.ok) {
        throw new Error(`Error fetching batch logs: ${response.status}`);
      }

      const data = await response.json() as { data?: BatchLog[], error?: string };

      if (data.error) {
        throw new Error(data.error);
      }

      setBatchLogs(data.data || []);
    } catch (err) {
      console.error('Error fetching batch logs:', err);
      showNetworkErrorToast(() => fetchBatchLogs(batchId), { customMessage: 'Failed to load batch logs' });
    }
  };

  // Calculate batch statistics
  const calculateStats = (batchData: Batch[]): BatchStatsData => {
    const totalBatches = batchData.length;
    const activeBatches = batchData.filter(batch => batch.status === 'in-progress').length;
    const completedBatches = batchData.filter(batch => batch.status === 'completed').length;
    const pendingBatches = batchData.filter(batch => batch.status === 'pending').length;
    const onHoldBatches = batchData.filter(batch => batch.status === 'on-hold').length;
    const totalQuantityPlanned = batchData.reduce((sum, batch) => sum + (batch.quantityPlanned || 0), 0);
    const totalQuantityProduced = batchData.reduce((sum, batch) => sum + (batch.quantityProduced || 0), 0);

    return {
      totalBatches,
      activeBatches,
      completedBatches,
      pendingBatches,
      onHoldBatches,
      totalQuantityPlanned,
      totalQuantityProduced,
    };
  };

  // Update statistics when batches change
  useEffect(() => {
    if (batches.length > 0) {
      const newStats = calculateStats(batches);
      setStats(newStats);
      setIsLoadingStats(false);
    } else if (!isLoading) {
      setIsLoadingStats(false);
    }
  }, [batches, isLoading]);

  // Table actions
  const tableActions: BatchesTableActions = useMemo(() => ({
    onView: (batch: BatchColumnData) => {
      const originalBatch = batches.find(b => b._id === batch._id);
      if (originalBatch) {
        setSelectedBatch(originalBatch);
        fetchBatchLogs(originalBatch._id);
        setIsViewDialogOpen(true);
      }
    },
    onEdit: (batch: BatchColumnData) => {
      openDialog(batch);
    },
    onDelete: (batch: BatchColumnData) => {
      const originalBatch = batches.find(b => b._id === batch._id);
      if (originalBatch) {
        setSelectedBatch(originalBatch);
        setIsDeleteDialogOpen(true);
      }
    },
  }), [batches]);

  // Table columns
  const columns = useMemo(() =>
    createBatchesColumns(tableActions),
    [tableActions]
  );

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  // Filtered data for StandardizedTable
  const filteredBatches = useMemo(() => {
    return batches.filter(batch => {
      const matchesSearch = !searchTerm ||
        batch.batchCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        batch.notes?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'all' || batch.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [batches, searchTerm, statusFilter]);

  // Open dialog for adding or editing a batch
  const openDialog = (batch?: BatchColumnData | null) => {
    setCurrentBatchAction(batch ? 'edit' : 'add');
    setCurrentBatch(batch as Batch | null);

    if (batch) {
      // Set form values for editing
      reset({
        batchCode: batch.batchCode,
        partId: typeof batch.partId === 'string' ? batch.partId : (batch.partId as Part)?._id,
        assemblyId: typeof batch.assemblyId === 'string' ? batch.assemblyId : (batch.assemblyId as Assembly)?._id,
        quantityPlanned: batch.quantityPlanned,
        quantityProduced: batch.quantityProduced || 0,
        startDate: format(new Date(batch.startDate), 'yyyy-MM-dd'),
        endDate: batch.endDate ? format(new Date(batch.endDate), 'yyyy-MM-dd') : undefined,
        status: batch.status,
        notes: batch.notes || '',
        workOrderId: typeof batch.workOrderId === 'string' ? batch.workOrderId : (batch.workOrderId as WorkOrder)._id
      });
    } else {
      // Reset form for adding
      reset({
        batchCode: '',
        partId: '',
        assemblyId: '',
        quantityPlanned: 1,
        quantityProduced: 0,
        startDate: format(new Date(), 'yyyy-MM-dd'),
        status: 'pending',
        notes: '',
        workOrderId: ''
      });
    }

    setIsDialogOpen(true);
  };

  // Close dialog
  const closeDialog = () => {
    setIsDialogOpen(false);
    setCurrentBatch(null);
    reset();
  };

  // Open view dialog for a batch
  const openViewDialog = (batch: Batch) => {
    setSelectedBatch(batch);
    fetchBatchLogs(batch._id);
    setIsViewDialogOpen(true);
  };

  // Close view dialog
  const closeViewDialog = () => {
    setIsViewDialogOpen(false);
    setSelectedBatch(null);
    setBatchLogs([]);
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (batch: Batch) => {
    setSelectedBatch(batch);
    setIsDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setSelectedBatch(null);
  };

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof batchFormSchema>) => {
    setIsLoadingAction(true);
    try {
      const method = currentBatchAction === 'edit' ? 'PUT' : 'POST';
      const url = currentBatchAction === 'edit' && currentBatch
        ? `/api/batches/${currentBatch._id}`
        : '/api/batches';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`Error ${currentBatchAction === 'edit' ? 'updating' : 'creating'} batch: ${response.status}`);
      }

      const responseData = asApiResponse<Batch>(await response.json());

      if (hasApiError(responseData)) {
        throw new Error(extractApiError(responseData) || 'Unknown error occurred');
      }

      showSuccessToast(`Batch ${currentBatchAction === 'edit' ? 'updated' : 'created'} successfully`);
      closeDialog();
      fetchBatches();
    } catch (err) {
      console.error(`Error ${currentBatchAction === 'edit' ? 'updating' : 'creating'} batch:`, err);
      showErrorToast({ error: err instanceof Error ? err.message : String(err) });
    } finally {
      setIsLoadingAction(false);
    }
  };

  // Delete a batch
  const deleteBatch = async () => {
    if (!selectedBatch) return;

    setIsLoadingAction(true);
    try {
      const response = await fetch(`/api/batches/${selectedBatch._id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`Error deleting batch: ${response.status}`);
      }

      const data = asApiResponse<{ message: string }>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      showSuccessToast('Batch deleted successfully');
      closeDeleteDialog();
      fetchBatches();
    } catch (err) {
      console.error('Error deleting batch:', err);
      showErrorToast({ error: err instanceof Error ? err.message : String(err) });
    } finally {
      setIsLoadingAction(false);
    }
  };

  // Update batch status
  const updateBatchStatus = async (batch: Batch, newStatus: string) => {
    setIsLoadingAction(true);
    try {
      const response = await fetch(`/api/batches/${batch._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error(`Error updating batch status: ${response.status}`);
      }

      const data = asApiResponse<Batch>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      showSuccessToast(`Batch status updated to ${newStatus}`);
      fetchBatches();
    } catch (err) {
      console.error('Error updating batch status:', err);
      showErrorToast({ error: err instanceof Error ? err.message : String(err) });
    } finally {
      setIsLoadingAction(false);
    }
  };

  const handleDelete = (batchId: string) => {
    if (confirm('Are you sure you want to delete this batch?')) {
      // Proceed with deletion
      deleteBatch();
    }
  };

  const getQualityStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 bg-yellow-200 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 rounded-full text-xs">Pending</span>;
      case 'approved':
        return <span className="px-2 py-1 bg-green-200 dark:bg-green-900/30 text-green-800 dark:text-green-400 rounded-full text-xs">Approved</span>;
      case 'rejected':
        return <span className="px-2 py-1 bg-red-200 dark:bg-red-900/30 text-red-800 dark:text-red-400 rounded-full text-xs">Rejected</span>;
      default:
        return <span className="px-2 py-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded-full text-xs">{status}</span>;
    }
  };

  const getExpiryStatus = (expiryDate: string | null) => {
    if (!expiryDate) return null;

    const today = new Date();
    const expiry = new Date(expiryDate);
    const warningDate = addDays(today, 30); // 30 days warning

    if (isBefore(expiry, today)) {
      return <span className="px-2 py-1 bg-red-200 dark:bg-red-900/30 text-red-800 dark:text-red-400 rounded-full text-xs">Expired</span>;
    } else if (isBefore(expiry, warningDate)) {
      return <span className="px-2 py-1 bg-yellow-200 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 rounded-full text-xs">Expiring Soon</span>;
    } else {
      return <span className="px-2 py-1 bg-green-200 dark:bg-green-900/30 text-green-800 dark:text-green-400 rounded-full text-xs">Valid</span>;
    }
  };



  // Get status badge style
  const getStatusBadge = (status: string) => {
    let className = '';

    switch (status.toLowerCase()) {
      case 'pending':
        className = 'bg-warning/10 text-warning';
        break;
      case 'in_progress':
        className = 'bg-info/10 text-info';
        break;
      case 'completed':
        className = 'bg-success/10 text-success';
        break;
      case 'cancelled':
        className = 'bg-destructive/10 text-destructive';
        break;
      case 'on_hold':
        className = 'bg-orange/10 text-orange';
        break;
      default:
        className = 'bg-muted/50 text-muted-foreground';
    }

    return (
      <Badge className={className}>
        {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
      </Badge>
    );
  };

  // Get part or assembly name
  const getItemName = (batch: Batch) => {
    if (batch.partId) {
      if (typeof batch.partId === 'string') {
        const part = parts.find(p => p._id === batch.partId);
        return part ? part.name : batch.partId;
      } else {
        return (batch.partId as Part).name;
      }
    } else if (batch.assemblyId) {
      if (typeof batch.assemblyId === 'string') {
        const assembly = assemblies.find(a => a._id === batch.assemblyId);
        return assembly ? assembly.name : batch.assemblyId;
      } else {
        return (batch.assemblyId as Assembly).name;
      }
    }
    return 'N/A';
  };

  // Get work order number
  const getWorkOrderNumber = (batch: Batch | null | undefined): string => {
    if (!batch || !batch.workOrderId) {
      return 'N/A';
    }
    if (typeof batch.workOrderId === 'string') {
      const workOrder = workOrders.find(wo => wo._id === batch.workOrderId);
      return workOrder?.woNumber ?? 'N/A';
    } else {
      return (batch.workOrderId as WorkOrder)?.woNumber ?? 'N/A';
    }
  };



  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Batch Tracking" />

      <div className="px-8 pb-8">
        {/* Batch Statistics */}
        <BatchStats stats={stats} isLoading={isLoadingStats} className="mb-6" />

        {error && (
          <div className="bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded mb-4">
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={fetchBatches}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        )}

        {/* Batch Table */}
        <StandardizedTable
          data={filteredBatches}
          columns={columns}
          searchPlaceholder="Search batches..."
          defaultViewMode={viewMode}
          enableSearch={true}
          enableViewToggle={true}
          onViewModeChange={setViewMode}
          onSearchChange={setSearchTerm}
          renderFilters={() => (
            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="on-hold">On Hold</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          )}
          renderActions={() => (
            <Button
              onClick={() => openDialog()}
              className="bg-primary hover:bg-primary/90"
            >
              <Plus size={16} className="mr-2" />
              New Batch
            </Button>
          )}
          // FIXED: Updated from legacy tableProps pattern to direct props pattern
          isLoading={isLoading}
          error={error ? new Error(error) : null}
          enableSorting={true}
          enableFiltering={true}
          enablePagination={true}
          enableColumnVisibility={false}
          mobileDisplayMode="cards"
          density="normal"
          initialPagination={{ pageIndex: 0, pageSize: 10 }}
          pageSizeOptions={[10, 20, 50, 100]}
        />

        {/* Batch Form Modal */}
        {isDialogOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  {currentBatch ? 'Edit Batch' : 'Create Batch'}
                </h2>
              </div>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Batch Code</label>
                    <input
                      type="text"
                      {...register('batchCode')}
                      readOnly={!!currentBatch}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    {errors.batchCode && <p className="text-red-500 text-xs mt-1">{errors.batchCode.message}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Part</label>
                    <select
                      {...register('partId', { required: 'Part is required' })}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    >
                      <option value="">Select Part</option>
                      {parts.map((part) => (
                        <option key={part._id} value={part._id}>
                          {part.name}
                        </option>
                      ))}
                    </select>
                    {errors.partId && <p className="text-red-500 text-xs mt-1">{errors.partId.message}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Quantity Planned</label>
                    <input
                      type="number"
                      {...register('quantityPlanned', {
                        required: 'Quantity is required',
                        min: { value: 1, message: 'Quantity must be at least 1' }
                      })}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    {errors.quantityPlanned && <p className="text-red-500 text-xs mt-1">{errors.quantityPlanned.message}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Start Date</label>
                    <input
                      type="date"
                      {...register('startDate', { required: 'Start date is required' })}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    {errors.startDate && <p className="text-red-500 text-xs mt-1">{errors.startDate.message}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">End Date</label>
                    <input
                      type="date"
                      {...register('endDate')}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                    <select
                      {...register('status', { required: 'Status is required' })}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    >
                      <option value="pending">Pending</option>
                      <option value="in_progress">In Progress</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                      <option value="on_hold">On Hold</option>
                    </select>
                    {errors.status && <p className="text-red-500 text-xs mt-1">{errors.status.message}</p>}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes</label>
                    <textarea
                      {...register('notes')}
                      rows={3}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    ></textarea>
                  </div>
                </div>

                <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={closeDialog}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-yellow-300 dark:bg-yellow-600 text-gray-800 dark:text-gray-100 rounded-md hover:bg-yellow-400 dark:hover:bg-yellow-700"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Saving...' : 'Save Batch'}
                  </button>
                </div>
              </form>
            </motion.div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BatchTracking;
