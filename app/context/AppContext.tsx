"use client";

import React, {
    createContext,
    ReactNode,
    useContext,
    useEffect,
    useState,
    useCallback,
} from 'react';
// Import API utilities instead of direct MongoDB access
import {
    AssemblyItem,
    LogisticsInfo,
    OrderStatusCount,
    StockStatus,
    Product,
    HierarchicalComponent
} from '@/app/types';
import { Part } from '@/app/types/inventory';
import { getApiUrl } from '@/app/utils/env';

/**
 * Extended Part type for frontend use that includes additional fields
 * This combines the database Part structure with frontend-specific fields
 */
interface InventoryPart extends Omit<Part, 'createdAt' | 'updatedAt'> {
  // Frontend compatibility fields
  id: string; // Mapped from _id
  partNumber: string; // Already in Part but ensuring it's required
  businessName?: string | null; // Business name for the part

  // Additional frontend fields
  supplierManufacturer?: string;
  supplierId?: any; // Can be string or populated object
  category?: string;
  categoryId?: string;
  cost?: number;
  costPrice: number;
  onHandValue?: number;
  demand?: 'High' | 'Medium' | 'Low';
  location?: string; // Top-level location for backward compatibility
  unitOfMeasure: string;

  // Date fields as strings for frontend
  createdAt: string;
  updatedAt: string;

  // Assembly-related fields
  isAssembly?: boolean;
  subParts?: any[];
  schemaVersion?: number;
}

/**
 * Type definition for the application context
 * Contains all the data and functions needed throughout the application
 */
interface AppContextType {
  /** List of all products in the inventory */
  products: InventoryPart[];
  /** Current stock status information */
  stockStatus: StockStatus;
  /** Order status counts by category */
  orderStatus: OrderStatusCount;
  /** Logistics information */
  logisticsInfo: LogisticsInfo;
  /** List of assembly items */
  assemblies: AssemblyItem[];
  /** Distribution of products by category */
  categoryDistribution: Record<string, number>;
  /** Distribution of products by supplier */
  supplierDistribution: Record<string, number>;
  /** Inventory value by category */
  inventoryValueByCategory: Record<string, number>;
  /** List of high demand items */
  highDemandItems: InventoryPart[];
  /** Whether data is currently loading */
  isLoading: boolean;
  /** Error message if any */
  error: string | null;
  /** Whether the sidebar is expanded */
  sidebarExpanded: boolean;
  /** Function to set sidebar expanded state */
  setSidebarExpanded: (expanded: boolean) => void;
  /** Current location/warehouse */
  currentLocation: string;
  /** Function to set current location */
  setCurrentLocation: (location: string) => void;
  /** Current time frame for data */
  timeFrame: string;
  /** Function to set time frame */
  setTimeFrame: (timeFrame: string) => void;
  /** Function to refresh all data */
  refreshData: () => Promise<void>;
  /** Whether the app is using mock data */
  isUsingMockData: boolean;
  /** Function to add a new product */
  addProduct: (formData: Partial<InventoryPart>) => Promise<void>;
  /** Function to update an existing product */
  updateProduct: (id: string, formData: Partial<InventoryPart>) => Promise<void>;
  /** Function to delete a product */
  deleteProduct: (id: string) => Promise<void>;
  /** Function to get products with pagination */
  getProducts: (options?: { page?: number, limit?: number, search?: string }) => Promise<{
    products: InventoryPart[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    }
  }>;

  /** Function to create a new product with hierarchical BOM */
  createProduct: (productData: {
    productCode: string;
    name: string;
    description: string;
    categoryId: string;
    status: 'active' | 'discontinued' | 'in_development';
    sellingPrice: number;
    components?: HierarchicalComponent[];
  }) => Promise<Product>;

  /** Function to update a product with hierarchical BOM */
  updateProductById: (id: string, updateData: {
    name?: string;
    description?: string;
    categoryId?: string;
    status?: 'active' | 'discontinued' | 'in_development';
    sellingPrice?: number;
    components?: HierarchicalComponent[];
  }) => Promise<Product>;

  /** Function to get a single product by ID with BOM data */
  getProductById: (id: string, includeAssembly?: boolean) => Promise<Product>;

  /** Function to delete a product */
  deleteProductById: (id: string) => Promise<void>;

  /** Centralized pagination state for products */
  productsPagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
  /** Function to update pagination state */
  setProductsPagination: (pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  }) => void;
}

/**
 * Context for the application state
 * Initialized as undefined and will be populated by the AppProvider
 */
const AppContext = createContext<AppContextType | undefined>(undefined);

/**
 * Provider component for the application context
 * Manages the global state of the application
 * Handles data fetching, state updates, and provides methods for CRUD operations
 */
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // State for data
  const [products, setProducts] = useState<InventoryPart[]>([]);
  const [stockStatus, setStockStatus] = useState<StockStatus>({
    inStock: 0,
    lowStock: 0,
    outOfStock: 0,
    totalItems: 0,
    total: 0,
    overstock: 0
  });
  const [orderStatus, setOrderStatus] = useState<OrderStatusCount>({
    pending: 0,
    processing: 0,
    shipped: 0,
    delivered: 0,
    total: 0,
    completed: 0,
    cancelled: 0
  });
  const [logisticsInfo, setLogisticsInfo] = useState<LogisticsInfo>({
    inTransit: 0,
    delivered: 0,
    delayed: 0,
    total: 0,
    pending: 0,
    totalShipments: 0
  });
  const [assemblies, setAssemblies] = useState<AssemblyItem[]>([]);

  // State for UI
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [currentLocation, setCurrentLocation] = useState('All Locations');
  const [timeFrame, setTimeFrame] = useState('This Month');
  const [isUsingMockData, setIsUsingMockData] = useState(false);
  console.log('[AppContext] Initial isUsingMockData state:', false);

  // New state for pagination cache
  const [paginationCache, setPaginationCache] = useState<{
    [key: string]: {
      products: InventoryPart[];
      timestamp: number;
      pagination: {
        total: number;
        page: number;
        limit: number;
        pages: number;
      }
    }
  }>({});

  // Cache timeout (10 minutes)
  const CACHE_TIMEOUT = 10 * 60 * 1000;

  // Centralized pagination state for products
  const [productsPagination, setProductsPagination] = useState({
    total: 0,
    page: 1,
    limit: 50,
    pages: 1
  });

  // Generate a unique part ID
  const generatePartId = () => {
    const prefix = 'P';
    const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${prefix}${randomNum}`;
  };

  // Calculate derived data
  const categoryDistribution = products.reduce((acc, product) => {
    // Use part status as category
    const category = product.category || 'Uncategorized';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const supplierDistribution = products.reduce((acc, product) => {
    const supplier = product.supplierManufacturer || 'Unknown';
    acc[supplier] = (acc[supplier] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const inventoryValueByCategory = products.reduce((acc, product) => {
    const category = product.category || 'Uncategorized';
    const value = (product.inventory?.currentStock || 0) * (product.cost || 0);
    acc[category] = (acc[category] || 0) + value;
    return acc;
  }, {} as Record<string, number>);

  const highDemandItems = products
    .filter((p) => p.demand === 'High')
    .sort((a, b) => (b.inventory?.currentStock || 0) - (a.inventory?.currentStock || 0))
    .slice(0, 5);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Reduce excessive logging for better performance
      console.log('[AppContext] Starting data fetch');

      // Try to fetch parts data with inventory information
      let partsData = [];
      let useRealData = false;
      let connectionError = null;

      // Store the total count from pagination for later use
      let totalPartsCount = 0;

      try {
        // Fetch a reasonable amount of data for dashboard metrics
        const response = await fetch(getApiUrl('/api/parts?page=1&limit=50'));
        if (response.status !== 200) {
          console.warn('[AppContext] Parts API returned status:', response.status);
        }

        // Add detailed error handling for JSON parsing
        let result;
        try {
          const responseText = await response.text();
          console.log('[FRONTEND DEBUG] Raw API response text:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

          try {
            result = JSON.parse(responseText);
            console.log('[FRONTEND DEBUG] Parsed API response structure:', {
              hasData: !!result.data,
              dataType: typeof result.data,
              dataIsArray: Array.isArray(result.data),
              dataLength: result.data ? result.data.length : 'N/A',
              hasPagination: !!result.pagination,
              paginationTotalCount: result.pagination ? result.pagination.totalCount : 'N/A',
              hasError: !!result.error,
              error: result.error,
              keys: Object.keys(result)
            });
          } catch (parseError: any) {
            console.error('[FRONTEND DEBUG] JSON parse error:', parseError);
            throw new Error(`Failed to parse API response: ${parseError.message || 'Unknown parse error'}. Raw response: ${responseText.substring(0, 100)}...`);
          }
        } catch (textError: any) {
          console.error('[FRONTEND DEBUG] Error getting response text:', textError);
          throw new Error(`Failed to get response text: ${textError.message || 'Unknown text error'}`);
        }

        if (!response.ok) {
          console.error('[FRONTEND DEBUG] API response not OK:', response.status, result.error);
          throw new Error(result.error || 'Failed to fetch parts');
        }

        partsData = result.data || [];
        // Store the total count from pagination
        totalPartsCount = result.pagination?.totalCount || partsData.length;
        // Log pagination information
        console.log('[FRONTEND DEBUG] Parts API pagination info:', result.pagination);
        console.log('[FRONTEND DEBUG] Parts API received data count:', partsData.length);
        console.log('[FRONTEND DEBUG] Parts API total items:', totalPartsCount);

        // ENHANCED DEBUG LOGGING: Log detailed structure of first part from API
        if (partsData.length > 0) {
          const firstPart = partsData[0];
          console.log('[FRONTEND DEBUG] ===== API RESPONSE STRUCTURE ANALYSIS =====');
          console.log('[FRONTEND DEBUG] First part complete structure:', JSON.stringify(firstPart, null, 2));
          console.log('[FRONTEND DEBUG] First part inventory object:', JSON.stringify(firstPart.inventory, null, 2));
          console.log('[FRONTEND DEBUG] First part has inventory?', !!firstPart.inventory);
          console.log('[FRONTEND DEBUG] First part inventory.currentStock:', firstPart.inventory?.currentStock);
          console.log('[FRONTEND DEBUG] ===== END API RESPONSE ANALYSIS =====');
        } else {
          console.log('[FRONTEND DEBUG] No parts in API response array');
        }

        if (partsData.length > 0) {
          // Use real data if available
          useRealData = true;
          setIsUsingMockData(false);
        } else {
          connectionError = 'No parts found in database. Using sample data instead.';
        }
      } catch (error) {
        console.error('[AppContext] Error fetching parts:', error);
        connectionError = `Failed to fetch parts: ${error instanceof Error ? error.message : String(error)}`;
      }

      if (!useRealData) {
        console.log('[FRONTEND DEBUG] Attempting to use real data despite initial issues');
        // Instead of immediately falling back to mock data, try to fetch assemblies directly
        try {
          console.log('[FRONTEND DEBUG] Attempting direct fetch of assemblies');
          const assembliesResponse = await fetch(getApiUrl('/api/assemblies'));

          // Check content type before parsing
          const contentType = assembliesResponse.headers.get('content-type') || '';
          if (!contentType.includes('application/json')) {
            const body = await assembliesResponse.text();
            console.error('[FRONTEND DEBUG] Expected JSON, got:', body.substring(0, 200));
            throw new Error('Server returned HTML instead of JSON');
          }

          if (assembliesResponse.ok) {
            const assembliesResult = await assembliesResponse.json();
            if (assembliesResult.data && assembliesResult.data.length > 0) {
              console.log(`[FRONTEND DEBUG] Successfully fetched ${assembliesResult.data.length} assemblies directly`);
              // We have assemblies data, so we can use real data
              useRealData = true;
              setIsUsingMockData(false);
            } else {
              throw new Error('No assemblies found in direct fetch');
            }
          } else {
            const errorText = await assembliesResponse.text();
            console.error('[FRONTEND DEBUG] Assemblies API error:', errorText);
            throw new Error(`Assemblies fetch failed with status ${assembliesResponse.status}`);
          }
        } catch (directFetchError) {
          console.error('[AppContext] Direct fetch of assemblies failed:', directFetchError);
          setIsUsingMockData(true);
          setError(connectionError || 'Unable to fetch data from MongoDB. Using sample data instead.');

          // Initialize with empty data (will use mock data from UI components)
          setProducts([]);
          setStockStatus({ total: 0, lowStock: 0, outOfStock: 0, overstock: 0, inStock: 0, totalItems: 0 });
          setOrderStatus({ pending: 0, processing: 0, shipped: 0, delivered: 0, total: 0, completed: 0, cancelled: 0 });
          setLogisticsInfo({ inTransit: 0, delivered: 0, delayed: 0, total: 0, pending: 0, totalShipments: 0 });
          setAssemblies([]);

          console.warn('[AppContext] Using mock data due to database connectivity issues');

          // Stop here, don't try to fetch other data
          setIsLoading(false);
          return;
        }
      }
      console.log('[FRONTEND DEBUG] Using real data because useRealData is true');

      // Continue with the rest of the data fetching logic for real data
      // Fetch suppliers data
      let suppliersData;
      try {
        const response = await fetch(getApiUrl('/api/suppliers'));
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch suppliers');
        }

        suppliersData = result.data;
        console.log(`Fetched ${suppliersData?.length || 0} suppliers`);
      } catch (error) {
        console.error('Error fetching suppliers:', error);
        throw new Error(`Failed to fetch suppliers: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Fetch assemblies data
      let assembliesData = [];
      try {
        console.log('[FRONTEND DEBUG] Fetching assemblies from API');
        const response = await fetch(getApiUrl('/api/assemblies'));
        console.log('[FRONTEND DEBUG] Assemblies API response status:', response.status);

        // Add detailed error handling for JSON parsing
        let result;
        try {
          const responseText = await response.text();
          console.log('[FRONTEND DEBUG] Raw assemblies API response text:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

          try {
            result = JSON.parse(responseText);
          } catch (parseError: any) {
            console.error('[FRONTEND DEBUG] Assemblies JSON parse error:', parseError);
            throw new Error(`Failed to parse assemblies API response: ${parseError.message || 'Unknown parse error'}. Raw response: ${responseText.substring(0, 100)}...`);
          }
        } catch (textError: any) {
          console.error('[FRONTEND DEBUG] Error getting assemblies response text:', textError);
          throw new Error(`Failed to get assemblies response text: ${textError.message || 'Unknown text error'}`);
        }

        if (!response.ok) {
          console.error('[FRONTEND DEBUG] Assemblies API error:', result.error);
          throw new Error(result.error || 'Failed to fetch assemblies');
        }

        assembliesData = result.data || [];

        // Minimal logging for performance
        if (assembliesData.length === 0) {
          console.warn('[AppContext] No assemblies found in API response');
        }
      } catch (error) {
        console.error('[AppContext] Error fetching assemblies:', error);
        // Continue with empty assemblies data instead of throwing
        assembliesData = [];
      }

      // Fetch purchase orders data for order status card
      let purchaseOrdersData = [];
      try {
        const response = await fetch(getApiUrl('/api/purchase-orders'));

        // Add detailed error handling for JSON parsing
        let result;
        try {
          const responseText = await response.text();
          console.log('[FRONTEND DEBUG] Raw purchase orders API response text:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

          try {
            result = JSON.parse(responseText);
          } catch (parseError: any) {
            console.error('[FRONTEND DEBUG] Purchase orders JSON parse error:', parseError);
            throw new Error(`Failed to parse purchase orders API response: ${parseError.message || 'Unknown parse error'}. Raw response: ${responseText.substring(0, 100)}...`);
          }
        } catch (textError: any) {
          console.error('[FRONTEND DEBUG] Error getting purchase orders response text:', textError);
          throw new Error(`Failed to get purchase orders response text: ${textError.message || 'Unknown text error'}`);
        }

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch purchase orders');
        }

        purchaseOrdersData = result.data || [];
        console.log(`Fetched ${purchaseOrdersData.length} purchase orders`);
      } catch (error) {
        console.error('Error fetching purchase orders:', error);
        // Don't use mock data, just throw the error to be handled in the main catch block
        throw new Error(`Failed to fetch purchase orders: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Map parts and inventory data
      console.log('Mapping fetched data to application state');

      // Add defensive check before mapping partsData
      console.log('[FRONTEND DEBUG] ===== STARTING APPCONTEXT MAPPING =====');
      console.log('[FRONTEND DEBUG] partsData array length:', partsData.length);

      const mappedProducts = Array.isArray(partsData) ? partsData.map((part: any, index: number) => {
        console.log(`[FRONTEND DEBUG] ===== MAPPING PART ${index + 1}/${partsData.length} =====`);
        console.log(`[FRONTEND DEBUG] Part ID: ${part._id}`);
        try { // Add try...catch around individual part mapping
          // ENHANCED DEBUG LOGGING: Log complete raw part structure
          console.log(`[FRONTEND DEBUG] Part ${part._id} Complete Raw Data:`, JSON.stringify(part, null, 2));
          console.log(`[FRONTEND DEBUG] Part ${part._id} has inventory object?`, !!part.inventory);
          console.log(`[FRONTEND DEBUG] Part ${part._id} inventory structure:`, JSON.stringify(part.inventory, null, 2));

          // In MongoDB, inventory data is already included with the part
          const inventoryItem = part.inventory || {};
          console.log(`[FRONTEND DEBUG] Part ${part._id} Inventory:`, JSON.stringify(inventoryItem));

          // Find the corresponding supplier
          const supplier = part.supplierId || {};
          console.log(`[FRONTEND DEBUG] Part ${part._id} Supplier Raw:`, JSON.stringify(supplier));

          // Calculate cost and value (using default values if not available)
          const cost = part.costPrice || part.cost || 0;
          // Use canonical schema field access patterns - ONLY use nested inventory.currentStock
          const supplierName = typeof part.supplierId === 'object' ? part.supplierId?.name ?? '' : '';

          // Calculate currentStock from finished stockLevels only (ready for use/sale)
          // This represents goods that are ready for use or sale, not work-in-progress
          const currentStock = part.inventory?.stockLevels?.finished ??
            (part.inventory?.currentStock ?? 0);

          // Calculate onHandValue using the calculated currentStock
          const onHandValue = currentStock * cost;
          console.log(`[FRONTEND DEBUG] Part ${part._id} Calculated: cost=${cost}, currentStock=${currentStock} (from stockLevels=${JSON.stringify(part.inventory?.stockLevels)}), reorderLevel=${part.reorderLevel}, onHandValue=${onHandValue}, supplierName='${supplierName}'`);

          const mappedProduct: InventoryPart = {
            // Core Part fields
            _id: part._id.toString(),
            id: part._id.toString(),
            partNumber: part.partNumber || '',
            name: part.name || '',
            businessName: part.businessName || null, // NEW FIELD: Human-readable business name
            description: part.description || '',
            technicalSpecs: part.technicalSpecs || '',
            isManufactured: part.isManufactured || false,
            reorderLevel: part.reorderLevel ?? null,
            status: part.status || 'active',

            // Nested inventory object (canonical structure with new stockLevels support)
            inventory: {
              // Calculate currentStock from finished stockLevels only (ready for use/sale)
              currentStock: part.inventory?.stockLevels?.finished ??
                (part.inventory?.currentStock ?? 0),
              // Include the new stockLevels object
              stockLevels: part.inventory?.stockLevels || undefined,
              warehouseId: part.inventory?.warehouseId || '',
              safetyStockLevel: part.inventory?.safetyStockLevel ?? 0,
              maximumStockLevel: part.inventory?.maximumStockLevel ?? 0,
              averageDailyUsage: part.inventory?.averageDailyUsage ?? 0,
              abcClassification: part.inventory?.abcClassification || 'C',
              lastStockUpdate: part.inventory?.lastStockUpdate || null
            },

            // Frontend-specific fields
            supplierManufacturer: supplierName,
            supplierId: part.supplierId,
            category: part.category || 'Uncategorized',
            categoryId: part.categoryId,
            cost,
            costPrice: cost,
            onHandValue,
            demand: part.demand || 'Low',
            location: part.inventory?.location || '', // Top-level for backward compatibility
            unitOfMeasure: part.unitOfMeasure || 'pcs',

            // Timestamps
            createdAt: part.createdAt || new Date().toISOString(),
            updatedAt: part.updatedAt || new Date().toISOString(),

            // Assembly fields
            isAssembly: part.isAssembly || false,
            subParts: part.subParts || [],
            schemaVersion: part.schemaVersion || 1
          };

          // ENHANCED DEBUG LOGGING: Log mapped product structure
          console.log(`[FRONTEND DEBUG] ===== MAPPED INVENTORY PART STRUCTURE =====`);
          console.log(`[FRONTEND DEBUG] Part ${part._id} Mapped InventoryPart:`, JSON.stringify(mappedProduct, null, 2));
          console.log(`[FRONTEND DEBUG] Part ${part._id} Mapped has inventory?`, !!mappedProduct.inventory);
          console.log(`[FRONTEND DEBUG] Part ${part._id} Mapped inventory structure:`, JSON.stringify(mappedProduct.inventory, null, 2));
          console.log(`[FRONTEND DEBUG] Part ${part._id} Mapped inventory.currentStock:`, mappedProduct.inventory?.currentStock);
          console.log(`[FRONTEND DEBUG] Part ${part._id} NO MORE TOP-LEVEL currentStock - using only nested inventory.currentStock`);
          console.log(`[FRONTEND DEBUG] ===== END MAPPED INVENTORY PART =====`);

          return mappedProduct;
        } catch (mapError) {
          console.error(`[FRONTEND DEBUG] Error mapping part index ${index}, ID: ${part._id}`, mapError);
          console.error(`[FRONTEND DEBUG] Failing Part Raw Data:`, JSON.stringify(part));
          // Return null or a default object to avoid breaking the map, filter out later
          return null;
        }
      }).filter(p => p !== null) as InventoryPart[] : []; // Filter out nulls from failed mappings

      setProducts(mappedProducts);

      // ENHANCED DEBUG LOGGING: Log final products state
      console.log('[FRONTEND DEBUG] ===== FINAL APPCONTEXT PRODUCTS STATE =====');
      console.log(`[FRONTEND DEBUG] Set ${mappedProducts.length} products in state`);
      if (mappedProducts.length > 0) {
        const firstProduct = mappedProducts[0];
        if (firstProduct) {
          console.log('[FRONTEND DEBUG] First product in state:', JSON.stringify(firstProduct, null, 2));
          console.log('[FRONTEND DEBUG] First product has inventory?', !!firstProduct.inventory);
          console.log('[FRONTEND DEBUG] First product inventory structure:', JSON.stringify(firstProduct.inventory, null, 2));
          console.log('[FRONTEND DEBUG] First product currentStock (top-level):', firstProduct.inventory?.currentStock);
          console.log('[FRONTEND DEBUG] First product inventory.currentStock:', firstProduct.inventory?.currentStock);
        }
      }
      console.log('[FRONTEND DEBUG] ===== END APPCONTEXT PRODUCTS STATE =====');

      // Calculate stock status from real data using nested inventory structure
      if (mappedProducts.length > 0) {
        const outOfStock = mappedProducts.filter(p => (p.inventory?.currentStock ?? 0) === 0).length;
        const lowStock = mappedProducts.filter(p => (p.inventory?.currentStock ?? 0) > 0 && (p.inventory?.currentStock ?? 0) <= (p.reorderLevel ?? 0)).length;
        const overstock = mappedProducts.filter(p => (p.inventory?.currentStock ?? 0) > ((p.reorderLevel ?? 0) * 2)).length;

        // Use the total count from pagination that we stored earlier
        console.log('[FRONTEND DEBUG] Using total count from pagination:', totalPartsCount);

        setStockStatus({
          inStock: totalPartsCount - outOfStock - lowStock, // Assuming inStock is total - outOfStock - lowStock
          lowStock,
          outOfStock,
          totalItems: totalPartsCount,
          total: totalPartsCount, // Keep for backward compatibility if needed
          overstock
        });
      }

      // Map assemblies data if available
      if (Array.isArray(assembliesData) && assembliesData.length > 0) {
        try {
          console.log('[FRONTEND DEBUG] Mapping assemblies data, count:', assembliesData.length);

          const mappedAssemblies = assembliesData.map((assembly: any) => {
            try {
              console.log('[FRONTEND DEBUG] Processing assembly:', assembly.name, 'with', assembly.parts?.length || 0, 'parts');

            // Ensure assembly.parts is an array before mapping
            const assemblyParts = Array.isArray(assembly.parts) ? assembly.parts.map((part: any) => {
              try {
                // Log the part structure for debugging
                console.log('[FRONTEND DEBUG] Assembly part raw data:', part);

                // Safety check for part ID (supporting both new and legacy field names)
                // The part object can have different structures depending on the API response
                // 1. { partId: ObjectId } - New schema from MongoDB
                // 2. { partId: { _id, name, ... } } - Populated object from MongoDB
                // 3. { part_id: ObjectId } - Old schema from MongoDB
                // 4. { part_id: { _id, name, ... } } - Populated object from old schema

              console.log('[FRONTEND DEBUG] Part object structure:', {
                hasPartId: !!part.partId,
                hasPartIdObject: part.partId && typeof part.partId === 'object',
                hasPartIdId: part.partId && part.partId._id,
                hasPartIdName: part.partId && part.partId.name,
                hasPart_id: !!part.part_id,
                hasPart_idObject: part.part_id && typeof part.part_id === 'object',
                hasPart_idId: part.part_id && part.part_id._id,
                hasPart_idName: part.part_id && part.part_id.name
              });

              // Try to get the part ID from all possible locations
              let partIdObj = null;
              let partName = 'Unknown Part';

              // Check for partId (new schema)
              if (part.partId) {
                partIdObj = part.partId;
                // If partId is an object with name, use it
                if (typeof partIdObj === 'object' && partIdObj.name) {
                  partName = partIdObj.name;
                }
              }
              // If not found, check for part_id (old schema)
              else if (part.part_id) {
                partIdObj = part.part_id;
                // If part_id is an object with name, use it
                if (typeof partIdObj === 'object' && partIdObj.name) {
                  partName = partIdObj.name;
                }
              }

              if (!partIdObj) {
                console.warn('[FRONTEND DEBUG] Missing partId/part_id in assembly part');
                return {
                  id: 'unknown',
                  name: 'Unknown Part',
                  quantity: 0,
                  inStock: 0
                };
              }

              // Extract the ID string from the part ID object or string with robust error handling
              let partIdString = 'unknown';
              try {
                if (typeof partIdObj === 'object') {
                  // First try to get _id property if it exists
                  if (partIdObj._id) {
                    partIdString = partIdObj._id.toString();
                  }
                  // If no _id, try to get part_id property
                  else if (partIdObj.part_id) {
                    partIdString = typeof partIdObj.part_id === 'object'
                      ? (partIdObj.part_id._id?.toString() || 'unknown')
                      : partIdObj.part_id.toString();
                  }
                  // Last resort, try toString() but catch any errors
                  else {
                    try {
                      partIdString = partIdObj.toString();
                    } catch (e) {
                      console.error('[FRONTEND DEBUG] Error converting partIdObj to string:', e);
                      partIdString = 'unknown';
                    }
                  }
                } else if (partIdObj) {
                  // If it's not an object but has a value, convert to string
                  partIdString = partIdObj.toString();
                }
                // If we still have 'unknown', log a warning
                if (partIdString === 'unknown') {
                  console.warn('[FRONTEND DEBUG] Could not extract valid ID from part object:', partIdObj);
                }
              } catch (error) {
                console.error('[FRONTEND DEBUG] Error extracting part ID string:', error);
                partIdString = 'unknown';
              }

              console.log('[FRONTEND DEBUG] Extracted part ID:', partIdString);

              // Find the corresponding part in products to get current stock
              const productPart = mappedProducts.find(p => p.id === partIdString);

              if (productPart) {
                console.log('[FRONTEND DEBUG] Found matching part:', productPart.name);
                // Use the product part name if available
                partName = productPart.name;
              } else {
                console.warn('[FRONTEND DEBUG] No matching part found for ID:', partIdString);
              }

              // Get quantity from all possible field names
              const quantity = part.quantityRequired || part.quantity_required || part.quantity || 0;
              console.log('[FRONTEND DEBUG] Part quantity:', quantity);

              // We already have the part name from earlier processing
              return {
                id: partIdString,
                name: partName,
                quantity: quantity,
                inStock: (productPart as any)?.currentStock || 0
              };
              } catch (partError) {
                console.error('[FRONTEND DEBUG] Error processing assembly part:', partError);
                // Return a default part object to avoid breaking the map
                return {
                  id: 'error',
                  name: 'Error Processing Part',
                  quantity: 0,
                  inStock: 0
                };
              }
            }) : []; // If assembly.parts is not an array, use empty array

            return {
              id: assembly.assembly_id || assembly._id.toString(),
              name: assembly.name || 'Unnamed Assembly',
              stage: assembly.assembly_stage || 'FINAL ASSEMBLY',
              status: assembly.status || 'pending',
              progress: assembly.progress || 0,
              parts: assemblyParts
            };
            } catch (assemblyError) {
              console.error('[FRONTEND DEBUG] Error processing assembly:', assemblyError);
              // Return a default assembly object to avoid breaking the map
              return {
                id: assembly._id?.toString() || 'error',
                name: assembly.name || 'Error Processing Assembly',
                stage: 'ERROR',
                status: 'pending',
                progress: 0,
                parts: []
              };
            }
          });

          console.log('[FRONTEND DEBUG] Mapped assemblies:', mappedAssemblies.length);
          setAssemblies(mappedAssemblies);
        } catch (assemblyError) {
          console.error('[FRONTEND DEBUG] Error mapping assemblies:', assemblyError);
          setAssemblies([]);
        }
      } else {
        // Use empty array if no assemblies found
        console.log('[FRONTEND DEBUG] No assemblies data available, using empty array');
        setAssemblies([]);
      }

      // Calculate order status from purchase orders if available
      if (purchaseOrdersData && purchaseOrdersData.length > 0) {
        const pending = purchaseOrdersData.filter((po: any) => po.status === 'pending').length;
        const processing = purchaseOrdersData.filter((po: any) => po.status === 'processing').length;
        const shipped = purchaseOrdersData.filter((po: any) => po.status === 'shipped').length;
        const delivered = purchaseOrdersData.filter((po: any) => po.status === 'delivered').length;

        setOrderStatus({
          pending,
          processing,
          shipped,
          delivered,
          total: purchaseOrdersData.length,
          completed: 0, // Assuming 0 for now, needs actual calculation if data available
          cancelled: 0  // Assuming 0 for now, needs actual calculation if data available
        });

        // Update logistics info based on purchase orders
        setLogisticsInfo({
          inTransit: shipped,
          delivered,
          delayed: purchaseOrdersData.filter((po: any) => po.status === 'delayed').length,
          total: purchaseOrdersData.length,
          pending: purchaseOrdersData.filter((po: any) => po.status === 'pending').length,
          totalShipments: purchaseOrdersData.length
        });
      }

      // Data fetched successfully
      setIsUsingMockData(false);
    } catch (finalError) {
      console.error('[AppContext] Error in fetchData:', finalError);
      setError(`Failed to fetch data: ${finalError instanceof Error ? finalError.message : String(finalError)}`);
      setIsUsingMockData(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on initial load
  useEffect(() => {
    fetchData();
  }, []);

  // Add effect to log when isUsingMockData changes
  useEffect(() => {
    console.log('[FRONTEND DEBUG] isUsingMockData changed to:', isUsingMockData);
  }, [isUsingMockData]);

  const refreshData = async () => {
    await fetchData();
  };

  const addProduct = useCallback(async (formData: Partial<InventoryPart>) => {
    try {
      setIsLoading(true);
      console.log('[FRONTEND DEBUG] Adding product with data:', formData);

      // Structure the data with a nested inventory object using camelCase field names
      // This handles both new PartFormData with camelCase and legacy data with snake_case
      const requestData = {
        name: formData.name || 'New Product',
        description: formData.description || '', // Ensure description is always a string
        technicalSpecs: formData.technicalSpecs || '', // Use camelCase field name
        isManufactured: formData.isManufactured !== undefined ? formData.isManufactured : false,
        reorderLevel: formData.reorderLevel || 0,
        status: formData.status || 'active',
        inventory: {
          currentStock: formData.inventory?.currentStock || 0,
          warehouseId: formData.inventory?.warehouseId || '',
          safetyStockLevel: formData.inventory?.safetyStockLevel || 0,
          maximumStockLevel: formData.inventory?.maximumStockLevel || 0,
          averageDailyUsage: formData.inventory?.averageDailyUsage || 0,
          abcClassification: formData.inventory?.abcClassification || 'C',
          // Note: location field removed as it's not part of the PartInventory schema
          // Fix: Don't send lastStockUpdate if not provided, let API handle it
          ...(formData.inventory?.lastStockUpdate && { lastStockUpdate: formData.inventory.lastStockUpdate })
        },
        supplierId: formData.supplierId,
        unitOfMeasure: formData.unitOfMeasure || 'pcs',
        costPrice: formData.costPrice || 0,
        categoryId: formData.categoryId,
        // Fix: Generate partNumber if not provided instead of sending empty string
        partNumber: formData.partNumber || `P${Date.now().toString().slice(-6)}`,
        isAssembly: formData.isAssembly || false,
        schemaVersion: 1
      };

      console.log('[FRONTEND DEBUG] Structured request data for API:', JSON.stringify(requestData, null, 2));

      console.log('[FRONTEND DEBUG] Sending request data to API:', requestData);

      // Add to database using API
      const response = await fetch(getApiUrl('/api/parts'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();
      console.log('[FRONTEND DEBUG] API response:', result);

      if (!response.ok) {
        throw new Error(result.error || 'Failed to add part');
      }

      // Refresh data to include the new product
      await fetchData();
    } catch (error) {
      console.error('Error adding product:', error);
      setError(`Failed to add product: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  }, [fetchData, setIsLoading, setError]);

  const updateProduct = useCallback(async (id: string, formData: Partial<InventoryPart>) => {
    try {
      setIsLoading(true);

      // Structure the data using camelCase field names to match the MongoDB schema
      // Only include fields that are allowed by the UpdatePartDto interface
      const requestData: any = {};

      // Add allowed top-level fields only if they exist in formData
      if (formData.name !== undefined) requestData.name = formData.name;
      if (formData.businessName !== undefined) requestData.businessName = formData.businessName;
      if (formData.description !== undefined) requestData.description = formData.description || '';
      if (formData.technicalSpecs !== undefined) requestData.technicalSpecs = formData.technicalSpecs;
      if (formData.isManufactured !== undefined) requestData.isManufactured = formData.isManufactured;
      if (formData.reorderLevel !== undefined) requestData.reorderLevel = formData.reorderLevel;
      if (formData.status !== undefined) requestData.status = formData.status;
      if (formData.supplierId !== undefined) requestData.supplierId = formData.supplierId;
      if (formData.unitOfMeasure !== undefined) requestData.unitOfMeasure = formData.unitOfMeasure;
      if (formData.costPrice !== undefined) requestData.costPrice = formData.costPrice;
      if (formData.categoryId !== undefined) requestData.categoryId = formData.categoryId;

      // Add allowed inventory fields only if they exist in formData
      if (formData.inventory) {
        const inventoryData: any = {};
        if (formData.inventory.safetyStockLevel !== undefined) inventoryData.safetyStockLevel = formData.inventory.safetyStockLevel;
        if (formData.inventory.maximumStockLevel !== undefined) inventoryData.maximumStockLevel = formData.inventory.maximumStockLevel;
        if (formData.inventory.averageDailyUsage !== undefined) inventoryData.averageDailyUsage = formData.inventory.averageDailyUsage;
        if (formData.inventory.abcClassification !== undefined) inventoryData.abcClassification = formData.inventory.abcClassification;

        // Only add inventory object if it has allowed fields
        if (Object.keys(inventoryData).length > 0) {
          requestData.inventory = inventoryData;
        }
      }

      // Log the current stock value being sent
      console.log(`[FRONTEND DEBUG] Updating product ${id} with currentStock:`, formData.inventory?.currentStock);

      console.log('[FRONTEND DEBUG] Update product request data:', JSON.stringify(requestData, null, 2));

      // URL-encode the part ID to handle special characters like slashes
      const encodedId = encodeURIComponent(id);
      console.log(`[FRONTEND DEBUG] URL-encoded part ID for update API call: ${encodedId}`);

      // Update in database using API
      const response = await fetch(getApiUrl(`/api/parts/${encodedId}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update part');
      }

      // Log the API response to verify the data structure
      console.log('[FRONTEND DEBUG] Product update API response:', JSON.stringify(result, null, 2));

      // Instead of refreshing all data, update the specific product in the state
      // This ensures we see the immediate update in the UI
      if (result.data) {
        const updatedPart = result.data;
        const stockValue = updatedPart.inventory?.currentStock ?? 0;

        const finalId = typeof updatedPart._id === 'string' ? updatedPart._id : updatedPart._id.toString();

        const updatedProduct: InventoryPart = {
          // Core Part fields
          _id: finalId,
          id: finalId,
          partNumber: updatedPart.partNumber || '',
          name: updatedPart.name || '',
          description: updatedPart.description || '',
          technicalSpecs: updatedPart.technicalSpecs || '',
          isManufactured: updatedPart.isManufactured || false,
          reorderLevel: updatedPart.reorderLevel ?? null,
          status: updatedPart.status || 'active',

          // Nested inventory object (canonical structure)
          inventory: {
            currentStock: stockValue,
            warehouseId: updatedPart.inventory?.warehouseId || '',
            safetyStockLevel: updatedPart.inventory?.safetyStockLevel ?? 0,
            maximumStockLevel: updatedPart.inventory?.maximumStockLevel ?? 0,
            averageDailyUsage: updatedPart.inventory?.averageDailyUsage ?? 0,
            abcClassification: updatedPart.inventory?.abcClassification ?? 'C',
            lastStockUpdate: updatedPart.inventory?.lastStockUpdate || null
          },

          // Frontend-specific fields
          supplierManufacturer: updatedPart.supplierId?.name || '',
          supplierId: updatedPart.supplierId || null,
          category: updatedPart.category || 'Uncategorized',
          categoryId: updatedPart.categoryId || null,
          cost: updatedPart.costPrice || 0,
          costPrice: updatedPart.costPrice || 0,
          onHandValue: stockValue * (updatedPart.costPrice || 0),
          demand: updatedPart.demand || 'Low',
          location: updatedPart.inventory?.location || '',
          unitOfMeasure: updatedPart.unitOfMeasure || 'pcs',

          // Timestamps
          createdAt: updatedPart.createdAt || new Date().toISOString(),
          updatedAt: updatedPart.updatedAt || new Date().toISOString(),

          // Assembly fields
          isAssembly: updatedPart.isAssembly || false,
          subParts: updatedPart.subParts || [],
          schemaVersion: updatedPart.schemaVersion || 1
        };

        // Update the products state with the new product data
        setProducts(prevProducts => {
          const index = prevProducts.findIndex(p => p.id === id);
          if (index >= 0) {
            const newProducts = [...prevProducts];
            newProducts[index] = updatedProduct;
            return newProducts;
          }
          return prevProducts;
        });

        console.log('[FRONTEND DEBUG] Product updated in state:', updatedProduct);
      } else {
        // If we don't have the updated data, refresh all data
        await fetchData();
      }

      // Log the updated product
      console.log('[FRONTEND DEBUG] Product updated successfully:', result);
      return result.data;
    } catch (error: any) {
      console.error('Error updating product:', error);
      setError(`Failed to update product: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading, setError, setProducts, fetchData]);

  const deleteProduct = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      console.log(`[FRONTEND DEBUG] Deleting product with ID: ${id}`);

      // Ensure we're using the correct ID format for the API
      // The MongoDB service expects the _id field, which might be different from the id field
      // If the id looks like a UUID (contains hyphens), it's likely the correct MongoDB _id
      // Otherwise, we need to find the product in our state and use its _id
      let mongoId = id;

      // Check if the ID doesn't look like a MongoDB _id (UUID format with hyphens)
      if (!id.includes('-')) {
        console.log(`[FRONTEND DEBUG] ID ${id} doesn't look like a MongoDB _id, searching for product in state`);
        // Find the product in our state to get its _id
        const product = products.find(p => p.id === id);
        if (product && product._id) {
          mongoId = product._id;
          console.log(`[FRONTEND DEBUG] Found product in state, using _id: ${mongoId}`);
        } else {
          console.log(`[FRONTEND DEBUG] Couldn't find product with id ${id} in state, using original id`);
        }
      }

      console.log(`[FRONTEND DEBUG] Using MongoDB _id for delete API call: ${mongoId}`);

      // URL-encode the part ID to handle special characters like slashes
      const encodedMongoId = encodeURIComponent(mongoId);
      console.log(`[FRONTEND DEBUG] URL-encoded MongoDB _id for delete API call: ${encodedMongoId}`);

      // Delete the part and its inventory using API
      const response = await fetch(getApiUrl(`/api/parts/${encodedMongoId}`), {
        method: 'DELETE',
      });

      console.log(`[FRONTEND DEBUG] Delete API response status: ${response.status}`);

      // Add detailed error handling for JSON parsing
      let result;
      try {
        const responseText = await response.text();
        console.log('[FRONTEND DEBUG] Raw delete API response text:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

        try {
          result = JSON.parse(responseText);
        } catch (parseError: any) {
          console.error('[FRONTEND DEBUG] Delete JSON parse error:', parseError);
          throw new Error(`Failed to parse delete API response: ${parseError.message || 'Unknown parse error'}. Raw response: ${responseText.substring(0, 100)}...`);
        }
      } catch (textError: any) {
        console.error('[FRONTEND DEBUG] Error getting delete response text:', textError);
        throw new Error(`Failed to get delete response text: ${textError.message || 'Unknown text error'}`);
      }

      if (!response.ok) {
        console.error(`[FRONTEND DEBUG] Delete API error: ${result.error || 'Unknown error'}, Status: ${response.status}`);
        throw new Error(result.error || 'Failed to delete part');
      }

      console.log('[FRONTEND DEBUG] Delete API success:', result);

      // Update local state to remove the deleted product
      // Use both id and _id for filtering to ensure we remove the correct product
      setProducts(prevProducts => prevProducts.filter(product => {
        // If product has _id field, check against mongoId
        if (product._id) {
          return product._id !== mongoId;
        }
        // Otherwise, check against id
        return product.id !== id;
      }));

      // Optionally refresh all data to ensure consistency
      // await fetchData();

      return result;
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error deleting product:', error);
      setError(`Failed to delete product: ${error instanceof Error ? error.message : String(error)}`);
      throw error; // Re-throw to allow the component to handle the error
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading, setError, setProducts, fetchData]);

  // New function to get products with pagination and caching
  const getProducts = useCallback(async (options: { page?: number, limit?: number, search?: string } = {}) => {
    const page = options.page || 1;
    const limit = options.limit || 20;
    const search = options.search || '';

    console.log(`[APPCONTEXT DEBUG] getProducts called with options:`, options);
    console.log(`[APPCONTEXT DEBUG] Resolved parameters: page=${page}, limit=${limit}, search="${search}"`);

    // Create cache key
    const cacheKey = `page=${page}&limit=${limit}&search=${search}`;

    // Check cache first
    const now = Date.now();
    if (paginationCache[cacheKey] &&
        (now - paginationCache[cacheKey].timestamp) < CACHE_TIMEOUT) {
      // Update main products state with cached data
      setProducts(paginationCache[cacheKey].products);

      return {
        products: paginationCache[cacheKey].products,
        pagination: paginationCache[cacheKey].pagination
      };
    }

    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', page.toString());

      // Ensure limit is reasonable to prevent performance issues
      const finalLimit = Math.min(limit, 150); // Cap at 150 items per page to accommodate all parts
      params.append('limit', finalLimit.toString());

      if (search) {
        params.append('search', search);
        // Use search endpoint if a search term is provided
        const response = await fetch(`/api/parts/search?${params.toString()}`);

        // Add detailed error handling for JSON parsing
        let data;
        try {
          const responseText = await response.text();
          console.log('[FRONTEND DEBUG] Raw search API response text:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

          try {
            data = JSON.parse(responseText);
          } catch (parseError: any) {
            console.error('[FRONTEND DEBUG] Search JSON parse error:', parseError);
            throw new Error(`Failed to parse search API response: ${parseError.message || 'Unknown parse error'}. Raw response: ${responseText.substring(0, 100)}...`);
          }
        } catch (textError: any) {
          console.error('[FRONTEND DEBUG] Error getting search response text:', textError);
          throw new Error(`Failed to get search response text: ${textError.message || 'Unknown text error'}`);
        }

        if (!response.ok) {
          throw new Error(data.error || 'Failed to search parts');
        }

        // Process the raw API data to ensure field casing consistency
        const processedParts = Array.isArray(data.data) ? data.data.map((part: any) => {
          // Ensure critical fields use camelCase
          return {
            ...part,
            // Use canonical field names only
            businessName: part.businessName || null, // Add businessName field
            technicalSpecs: part.technicalSpecs || '',
            isManufactured: part.isManufactured || false,
            reorderLevel: part.reorderLevel || 0,
            supplierId: part.supplierId,
            unitOfMeasure: part.unitOfMeasure || 'pcs',
            costPrice: part.costPrice || 0,
            categoryId: part.categoryId,
            partNumber: part.partNumber || '',
            // Add status field if not present
            status: part.status || 'active',
            // Handle nested inventory object if it exists
            inventory: part.inventory ? {
              ...part.inventory,
              currentStock: part.inventory.currentStock || 0,
              safetyStockLevel: part.inventory.safetyStockLevel || 0,
              maximumStockLevel: part.inventory.maximumStockLevel || 0,
              averageDailyUsage: part.inventory.averageDailyUsage || 0,
              abcClassification: part.inventory.abcClassification || 'C',
              warehouseId: part.inventory.warehouseId,
              location: part.inventory.location || ''
            } : null
          };
        }) : [];

        // CRITICAL FIX: Map API pagination field names for search cache as well
        const searchPaginationForCache = data.pagination ? {
          total: data.pagination.totalCount || 0,
          page: data.pagination.currentPage || page,
          limit: data.pagination.limit || finalLimit,
          pages: data.pagination.totalPages || 0
        } : {
          total: 0,
          page,
          limit: finalLimit,
          pages: 0
        };

        // Update cache
        setPaginationCache(prev => ({
          ...prev,
          [cacheKey]: {
            products: processedParts,
            pagination: searchPaginationForCache,
            timestamp: Date.now()
          }
        }));

        // CRITICAL FIX: Update the main products state so UI components can access the data
        console.log('[FRONTEND DEBUG] getProducts (search) updating main state with', processedParts.length, 'products');
        setProducts(processedParts);

        // CRITICAL FIX: Map API pagination field names for search return as well
        const searchPaginationData = data.pagination ? {
          total: data.pagination.totalCount || 0,
          page: data.pagination.currentPage || page,
          limit: data.pagination.limit || finalLimit,
          pages: data.pagination.totalPages || 0
        } : {
          total: 0,
          page,
          limit: finalLimit,
          pages: 0
        };

        // Update centralized pagination state for search results
        setProductsPagination(searchPaginationData);

        return {
          products: processedParts,
          pagination: searchPaginationData
        };
      }

      // Use regular parts endpoint if no search term
      const response = await fetch(`/api/parts?${params.toString()}`);

      // Add detailed error handling for JSON parsing
      let data;
      try {
        const responseText = await response.text();
        console.log('[FRONTEND DEBUG] Raw parts API response text:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

        try {
          data = JSON.parse(responseText);
        } catch (parseError: any) {
          console.error('[FRONTEND DEBUG] Parts JSON parse error:', parseError);
          throw new Error(`Failed to parse parts API response: ${parseError.message || 'Unknown parse error'}. Raw response: ${responseText.substring(0, 100)}...`);
        }
      } catch (textError: any) {
        console.error('[FRONTEND DEBUG] Error getting parts response text:', textError);
        throw new Error(`Failed to get parts response text: ${textError.message || 'Unknown text error'}`);
      }

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch parts');
      }

        // Process the raw API data to ensure field casing consistency using canonical schema format
        const processedParts = Array.isArray(data.data) ? data.data.map((part: any): InventoryPart => {
          // Use canonical schema field access patterns
          const supplierName = typeof part.supplierId === 'object' ? part.supplierId?.name ?? '' : part.supplierName ?? '';
          const cost = part.costPrice || part.cost_price || 0;

          return {
            // Core Part fields
            _id: part._id.toString(),
            id: part._id.toString(),
            partNumber: part.partNumber || '',
            name: part.name || '',
            businessName: part.businessName || null, // Add businessName field
            description: part.description || '',
            technicalSpecs: part.technicalSpecs || '',
            isManufactured: part.isManufactured || false,
            reorderLevel: part.reorderLevel || null,
            status: part.status || 'active',

            // Nested inventory object (canonical structure)
            inventory: {
              currentStock: part.inventory?.currentStock || 0,
              warehouseId: part.inventory?.warehouseId || '',
              safetyStockLevel: part.inventory?.safetyStockLevel || 0,
              maximumStockLevel: part.inventory?.maximumStockLevel || 0,
              averageDailyUsage: part.inventory?.averageDailyUsage || 0,
              abcClassification: part.inventory?.abcClassification || 'C',
              lastStockUpdate: part.inventory?.lastStockUpdate || null
            },

            // Frontend-specific fields
            supplierManufacturer: supplierName,
            supplierId: part.supplierId,
            category: part.category || 'Uncategorized',
            categoryId: part.categoryId,
            cost,
            costPrice: cost,
            onHandValue: (part.inventory?.currentStock || 0) * cost,
            demand: part.demand || 'Low',
            location: part.inventory?.location || '',
            unitOfMeasure: part.unitOfMeasure || part.unit_of_measure || 'pcs',

            // Timestamps
            createdAt: part.createdAt || new Date().toISOString(),
            updatedAt: part.updatedAt || new Date().toISOString(),

            // Assembly fields
            isAssembly: part.isAssembly || false,
            subParts: part.subParts || [],
            schemaVersion: part.schemaVersion || 1
          };
        }) : [];

      // CRITICAL FIX: Map API pagination field names for cache as well
      const paginationForCache = data.pagination ? {
        total: data.pagination.totalCount || 0,
        page: data.pagination.currentPage || page,
        limit: data.pagination.limit || finalLimit,
        pages: data.pagination.totalPages || 0
      } : {
        total: 0,
        page,
        limit: finalLimit,
        pages: 0
      };

      // Update cache
      setPaginationCache(prev => ({
        ...prev,
        [cacheKey]: {
          products: processedParts,
          pagination: paginationForCache,
          timestamp: Date.now()
        }
      }));

      // CRITICAL FIX: Update the main products state so UI components can access the data
      console.log('[FRONTEND DEBUG] getProducts (regular) updating main state with', processedParts.length, 'products');
      setProducts(processedParts);

      // CRITICAL FIX: Map API pagination field names to expected format
      const paginationData = data.pagination ? {
        total: data.pagination.totalCount || 0,
        page: data.pagination.currentPage || page,
        limit: data.pagination.limit || finalLimit,
        pages: data.pagination.totalPages || 0
      } : {
        total: 0,
        page,
        limit: finalLimit,
        pages: 0
      };

      // Update centralized pagination state
      setProductsPagination(paginationData);

      return {
        products: processedParts,
        pagination: paginationData
      };
    } catch (error) {
      console.error('Error fetching products:', error);
      return {
        products: [],
        pagination: {
          total: 0,
          page,
          limit,
          pages: 0
        }
      };
    }
  }, [paginationCache, setPaginationCache, setProducts, setIsLoading, setProductsPagination]);

  // Product-specific methods for hierarchical BOM handling
  const createProduct = async (productData: {
    productCode: string;
    name: string;
    description: string;
    categoryId: string;
    status: 'active' | 'discontinued' | 'in_development';
    sellingPrice: number;
    components?: HierarchicalComponent[];
  }): Promise<Product> => {
    try {
      setIsLoading(true);
      console.log('[AppContext] Creating product with hierarchical BOM:', productData);

      const response = await fetch(getApiUrl('/api/products'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create product');
      }

      console.log('[AppContext] Product created successfully:', result.data);

      // Refresh data to include the new product
      await fetchData();

      return result.data;
    } catch (error) {
      console.error('[AppContext] Error creating product:', error);
      setError(`Failed to create product: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProductById = async (id: string, updateData: {
    name?: string;
    description?: string;
    categoryId?: string;
    status?: 'active' | 'discontinued' | 'in_development';
    sellingPrice?: number;
    components?: HierarchicalComponent[];
  }): Promise<Product> => {
    try {
      setIsLoading(true);
      console.log('[AppContext] Updating product with hierarchical BOM:', id, updateData);

      const response = await fetch(getApiUrl(`/api/products/${encodeURIComponent(id)}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update product');
      }

      console.log('[AppContext] Product updated successfully:', result.data);

      // Refresh data to reflect the changes
      await fetchData();

      return result.data;
    } catch (error) {
      console.error('[AppContext] Error updating product:', error);
      setError(`Failed to update product: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AppContext.Provider
      value={{
        products,
        stockStatus,
        orderStatus,
        logisticsInfo,
        assemblies,
        categoryDistribution,
        supplierDistribution,
        inventoryValueByCategory,
        highDemandItems,
        isLoading,
        error,
        sidebarExpanded,
        setSidebarExpanded,
        currentLocation,
        setCurrentLocation,
        timeFrame,
        setTimeFrame,
        refreshData,
        isUsingMockData,
        addProduct,
        updateProduct,
        deleteProduct,
        getProducts,
        // New product-specific methods
        createProduct,
        updateProductById,
        getProductById: async (id: string, includeAssembly: boolean = true): Promise<Product> => {
          try {
            setIsLoading(true);
            console.log('[AppContext] Fetching product by ID:', id, 'includeAssembly:', includeAssembly);

            const url = new URL(getApiUrl(`/api/products/${encodeURIComponent(id)}`));
            if (includeAssembly) {
              url.searchParams.set('includeAssembly', 'true');
            }

            const response = await fetch(url.toString());
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.error || 'Failed to fetch product');
            }

            console.log('[AppContext] Product fetched successfully:', result.data);
            return result.data;
          } catch (error) {
            console.error('[AppContext] Error fetching product:', error);
            setError(`Failed to fetch product: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
          } finally {
            setIsLoading(false);
          }
        },
        deleteProductById: async (id: string): Promise<void> => {
          try {
            setIsLoading(true);
            console.log('[AppContext] Deleting product:', id);

            const response = await fetch(getApiUrl(`/api/products/${encodeURIComponent(id)}`), {
              method: 'DELETE',
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.error || 'Failed to delete product');
            }

            console.log('[AppContext] Product deleted successfully');

            // Refresh data to reflect the deletion
            await fetchData();
          } catch (error) {
            console.error('[AppContext] Error deleting product:', error);
            setError(`Failed to delete product: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
          } finally {
            setIsLoading(false);
          }
        },
        // Centralized pagination state
        productsPagination,
        setProductsPagination,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

/**
 * Custom hook to access the application context
 * Must be used within an AppProvider component
 * @returns The application context with all data and functions
 * @throws Error if used outside of an AppProvider
 */
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
